<template>
  <div class="login-page">
    <div class="login-container">
      <!-- 左侧蓝色区域 -->
      <div class="login-left-side">
        <div class="hfut-logo">HFUT</div>
        <div class="welcome-content">
          <h1 class="welcome-title">Hej!</h1>
          <p class="welcome-subtitle">为大众创造更美好的<br>日常生活</p>
        </div>
      </div>

      <!-- 右侧白色区域 -->
      <div class="login-right-side">
        <div class="login-form-container">
          <!-- 系统标题 -->
          <div class="system-header">
            <div class="system-logo">🏡</div>
            <h1 class="system-title">芙蓉社区管理系统</h1>
            <p class="system-subtitle">Community Management System</p>
          </div>

          <!-- 登录表单标题 -->
          <div class="form-header">
            <h2 class="form-title">用户登录</h2>
          </div>

          <!-- 错误和成功消息显示 -->
          <div v-if="errorMessage" class="message error-message">
            ❌ {{ errorMessage }}
          </div>
          <div v-if="successMessage" class="message success-message">
            ✅ {{ successMessage }}
          </div>

          <form @submit.prevent="handleLogin" class="login-form">
            <div class="form-group">
              <label for="userName" class="form-label">
                <span class="label-icon">👤</span>
                用户名/手机号
              </label>
              <input
                type="text"
                id="userName"
                v-model="userName"
                required
                class="form-input"
                placeholder="qzzk"
              >
            </div>

            <div class="form-group">
              <label for="password" class="form-label">
                <span class="label-icon">🔒</span>
                密码
              </label>
              <input
                type="password"
                id="password"
                v-model="password"
                required
                class="form-input"
                placeholder="••••••"
              >
            </div>

            <div class="form-group">
              <label for="userType" class="form-label">
                <span class="label-icon">👥</span>
                用户类型
              </label>
              <select
                id="userType"
                v-model="userType"
                required
                class="form-input form-select"
              >
                <option value="">请选择用户类型</option>
                <option value="1">居民</option>
                <option value="2">网格员</option>
                <option value="3">社区管理员</option>
              </select>
            </div>

            <button type="submit" :disabled="isLoading" class="login-btn">
              <span v-if="isLoading" class="loading-spinner"></span>
              <span class="btn-text">{{ isLoading ? '登录中...' : '立即登录' }}</span>
            </button>

            <div class="form-footer">
              <p class="register-link">
                还没有账户？
                <router-link to="/register" class="register-btn">立即注册</router-link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { authApi } from '../services/authApi.js';
import { useAuthStore } from '../stores/auth.js';
import { useUserStore } from '../stores/userStore.js';

const router = useRouter();
const authStore = useAuthStore();
const userStore = useUserStore();

// 表单数据
const userName = ref('');
const password = ref('');
const userType = ref('');
const isLoading = ref(false);

// 错误和成功信息
const errorMessage = ref('');
const successMessage = ref('');

const handleLogin = async () => {
  // 清除之前的错误信息
  errorMessage.value = '';
  successMessage.value = '';

  // 前端验证
  if (!userName.value.trim()) {
    errorMessage.value = '请输入用户名或手机号';
    return;
  }

  if (!password.value) {
    errorMessage.value = '请输入密码';
    return;
  }

  if (password.value.length < 6) {
    errorMessage.value = '密码长度至少6位字符';
    return;
  }

  if (!userType.value) {
    errorMessage.value = '请选择用户类型';
    return;
  }

  isLoading.value = true;

  try {
    console.log('🔐 开始登录流程...');

    // 调试用户类型值
    console.log('🔍 调试用户类型:', {
      'userType.value': userType.value,
      'typeof userType.value': typeof userType.value,
      'parseInt(userType.value)': parseInt(userType.value),
      'isNaN(parseInt(userType.value))': isNaN(parseInt(userType.value))
    });

    // 构造请求数据（使用驼峰命名法）
    const loginData = {
      userName: userName.value.trim(),
      password: password.value,
      userType: parseInt(userType.value)
    };

    console.log('🚀 发送登录请求:', loginData);

    // 调用登录API
    const result = await authApi.login(loginData);

    console.log('📦 登录结果:', result);

    if (result.success) {
      successMessage.value = result.message || '登录成功！';

      // 设置认证状态
      console.log('💾 设置认证状态...');
      const expiresAt = new Date(Date.now() + 15 * 60 * 1000);
      console.log('⏰ Token过期时间:', expiresAt.toISOString());

      await authStore.setAuthData({
        user: {
          id: result.data?.id,
          userName: result.data?.userName,
          userType: result.data?.userType,
          role: result.data?.userType === 1 ? 'resident' : 'property'
        },
        token: result.data?.token,
        refreshToken: result.data?.refreshToken || '',
        expiresAt: expiresAt.toISOString(),
        rememberMe: false
      });

      // 同时更新 userStore 以兼容路由守卫
      // 优先使用API返回的userType，如果没有则使用表单中的userType
      const apiUserType = result.data?.userType;
      const formUserType = parseInt(userType.value);
      const finalUserTypeNumber = apiUserType || formUserType;
      const userTypeString = finalUserTypeNumber === 1 ? 'resident' : 'property';

      console.log('🔍 准备更新 userStore:', {
        'result.data.userType': result.data?.userType,
        'userType.value': userType.value,
        'formUserType': formUserType,
        'finalUserTypeNumber': finalUserTypeNumber,
        'userTypeString': userTypeString,
        'userData': {
          id: result.data?.id,
          userName: result.data?.userName,
          userType: finalUserTypeNumber
        }
      });

      userStore.login({
        id: result.data?.id,
        userName: result.data?.userName,
        userType: finalUserTypeNumber
      }, userTypeString);

      console.log('🔄 已同步更新 userStore:', {
        userType: userStore.userType,
        isAuthenticated: userStore.isAuthenticated,
        user: userStore.user
      });

      // 验证认证状态
      console.log('🔍 验证认证状态:', {
        isAuthenticated: authStore.isAuthenticated,
        hasToken: !!authStore.token,
        hasUser: !!authStore.user,
        isTokenExpired: authStore.isTokenExpired,
        tokenExpiry: authStore.tokenExpiry
      });

      // 等待响应式更新完成
      await nextTick();

      // 多次等待确保认证状态完全更新
      await new Promise(resolve => setTimeout(resolve, 100));
      await nextTick();

      // 再次确认认证状态
      console.log('🔍 跳转前最终认证状态:', {
        isAuthenticated: authStore.isAuthenticated,
        hasToken: !!authStore.token,
        hasUser: !!authStore.user,
        tokenExpiry: authStore.tokenExpiry,
        isTokenExpired: authStore.isTokenExpired
      });

      // 如果认证状态还是不正确，强制更新
      if (!authStore.isAuthenticated) {
        console.log('⚠️ 认证状态异常，强制更新...');
        authStore.restoreAuthState();
        await nextTick();
        console.log('🔍 强制更新后认证状态:', {
          isAuthenticated: authStore.isAuthenticated,
          hasToken: !!authStore.token,
          hasUser: !!authStore.user
        });
      }

      // 根据用户类型跳转到不同页面
      // 首先尝试从API响应中获取用户类型
      let finalUserType = result.data?.userType;

      // 如果API响应中没有用户类型，从localStorage中获取
      if (!finalUserType) {
        try {
          const userInfo = localStorage.getItem('user_info');
          if (userInfo) {
            const parsedUserInfo = JSON.parse(userInfo);
            finalUserType = parsedUserInfo.userType;
          }
        } catch (error) {
          console.error('❌ 解析localStorage用户信息失败:', error);
        }
      }

      // 如果还是没有用户类型，使用表单中选择的用户类型
      if (!finalUserType) {
        finalUserType = parseInt(userType.value);
      }

      console.log('🔍 检查用户类型:', {
        'result.data': result.data,
        'result.data.userType': result.data?.userType,
        'localStorage.userType': (() => {
          try {
            const userInfo = localStorage.getItem('user_info');
            return userInfo ? JSON.parse(userInfo).userType : null;
          } catch { return null; }
        })(),
        'userType.value': userType.value,
        'finalUserType': finalUserType
      });

      // 确定目标路径
      let targetPath;
      if (finalUserType === 1) {
        targetPath = '/resident-home';
        console.log('🏠 准备跳转到居民页面');
      } else if (finalUserType === 2 || finalUserType === 3) {
        targetPath = '/property-home';
        console.log('🏢 准备跳转到物业页面');
      } else {
        targetPath = '/dashboard';
        console.log('📊 准备跳转到默认页面，用户类型:', finalUserType);
      }

      console.log('🚀 准备跳转到:', targetPath);
      console.log('🔍 当前路径:', window.location.pathname);

      // 等待一下，确保所有状态都已更新
      await nextTick();

      // 检查localStorage中的数据
      console.log('🔍 localStorage数据检查:', {
        'userToken': localStorage.getItem('userToken'),
        'userType': localStorage.getItem('userType'),
        'userData': localStorage.getItem('userData')
      });

      // 再次确认状态
      console.log('🔍 跳转前最终状态确认:', {
        'authStore.isAuthenticated': authStore.isAuthenticated,
        'userStore.isAuthenticated': userStore.isAuthenticated,
        'userStore.userType': userStore.userType,
        'targetPath': targetPath
      });

      try {
        // 使用Vue Router replace进行跳转，避免历史记录问题
        console.log('🧭 使用Vue Router replace跳转...');
        await router.replace(targetPath);
        console.log('✅ Vue Router replace跳转成功');
      } catch (error) {
        console.error('❌ Vue Router replace跳转失败:', error);
        // 如果Vue Router失败，使用window.location
        console.log('🔄 使用window.location备用跳转...');
        window.location.replace(targetPath);
      }
    } else {
      errorMessage.value = result.message || '登录失败，请重试';
    }
  } catch (error) {
    console.error('❌ 登录失败:', error);
    errorMessage.value = error.message || '登录失败，请检查网络连接';
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: rgb(79, 146, 239);
  display: flex;
  align-items: stretch;
  justify-content: stretch;
}

.login-container {
  display: flex;
  width: 100vw;
  height: 100vh;
}

/* 左侧蓝色区域 - HFUT风格 */
.login-left-side {
  flex: 0 0 50%;
  background: #0058a3;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 80px 60px;
  color: white;
  position: relative;
  text-align: left;
}

.hfut-logo {
  position: absolute;
  top: 40px;
  left: 40px;
  font-size: 24px;
  font-weight: bold;
  color: white;
  background: #0058a3;
  padding: 8px 16px;
  border: 2px solid white;
  border-radius: 4px;
}

.welcome-content {
  max-width: 400px;
}

.welcome-title {
  font-size: 72px;
  font-weight: bold;
  color: #ffdb00;
  margin: 0 0 20px 0;
  line-height: 1;
}

.welcome-subtitle {
  font-size: 28px;
  font-weight: 500;
  color: white;
  margin: 0;
  line-height: 1.3;
}



/* 右侧白色区域 */
.login-right-side {
  flex: 1;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-form-container {
  background: white;
  border-radius: 0;
  padding: 60px 40px;
  width: 100%;
  max-width: 500px;
  box-shadow: none;
  border: none;
}

.system-header {
  text-align: center;
  margin-bottom: 40px;
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.system-logo {
  font-size: 48px;
  margin-bottom: 15px;
  color: #4CAF50;
}

.system-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0 0 8px 0;
}

.system-subtitle {
  font-size: 14px;
  color: #87CEEB;
  margin: 0 0 40px 0;
  font-weight: 400;
}

.form-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 30px 0;
  text-align: center;
  position: relative;
}

.form-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: #0058a3;
  border-radius: 1px;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.error-message {
  background-color: #fee;
  color: #c53030;
  border: 1px solid #feb2b2;
}

.success-message {
  background-color: #f0fff4;
  color: #38a169;
  border: 1px solid #9ae6b4;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.label-icon {
  font-size: 14px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 0;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #f8f9fa;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #0058a3;
  box-shadow: none;
  background: white;
}

.form-input::placeholder {
  color: #999;
}

.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
}

.form-select:focus {
  outline: none;
  border-color: #0058a3;
  box-shadow: none;
  background-color: white;
}

.login-btn {
  width: 100%;
  padding: 16px;
  background: #e0e0e0;
  color: #333;
  border: none;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
}

.login-btn:hover:not(:disabled) {
  background: rgb(34,170,255);
}

.login-btn:active {
  background: #c0c0c0;
}

.login-btn:disabled {
  background: #f0f0f0;
  color: #999;
  cursor: not-allowed;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255,255,255,0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.btn-text {
  font-weight: 600;
}

.form-footer {
  text-align: center;
  margin-top: 20px;
}

.register-link {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.register-btn {
  color: #4CAF50;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.register-btn:hover {
  color: #45a049;
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .login-container {
    flex-direction: column;
  }

  .login-left-side {
    flex: 0 0 40vh;
    padding: 40px 30px;
  }

  .login-right-side {
    flex: 1;
    padding: 20px;
  }

  .welcome-title {
    font-size: 48px;
  }

  .welcome-subtitle {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .login-left-side {
    padding: 30px 20px;
  }

  .login-form-container {
    padding: 40px 30px;
  }

  .system-title {
    font-size: 20px;
  }

  .system-logo {
    font-size: 40px;
  }

  .welcome-title {
    font-size: 36px;
  }

  .welcome-subtitle {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .login-form-container {
    padding: 30px 20px;
  }

  .form-input {
    padding: 12px 14px;
    font-size: 14px;
  }

  .login-btn {
    padding: 14px;
    font-size: 14px;
  }

  .hfut-logo {
    font-size: 20px;
    top: 20px;
    left: 20px;
  }

  .welcome-title {
    font-size: 28px;
  }

  .welcome-subtitle {
    font-size: 16px;
  }
}
</style>
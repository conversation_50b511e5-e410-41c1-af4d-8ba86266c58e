<template>
  <div v-if="visible" class="dialog-overlay" @click="handleOverlayClick">
    <div class="dialog-container" @click.stop>
      <!-- 对话框头部 -->
      <div class="dialog-header" :class="headerClass">
        <div class="dialog-icon">
          <span>{{ iconText }}</span>
        </div>
        <h3 class="dialog-title">{{ title }}</h3>
        <button v-if="showCloseButton" class="close-button" @click="handleCancel">
          ×
        </button>
      </div>

      <!-- 对话框内容 -->
      <div class="dialog-content">
        <p class="dialog-message">{{ message }}</p>
        <div v-if="inputType" class="dialog-input">
          <textarea
            v-if="inputType === 'textarea'"
            v-model="inputValue"
            :placeholder="inputPlaceholder"
            class="input-field textarea-field"
            rows="4"
          ></textarea>
          <input
            v-else
            v-model="inputValue"
            :type="inputType"
            :placeholder="inputPlaceholder"
            class="input-field"
          />
        </div>
      </div>

      <!-- 对话框按钮 -->
      <div class="dialog-actions">
        <button
          v-if="showCancelButton"
          class="dialog-button cancel-button"
          @click="handleCancel"
        >
          {{ cancelText }}
        </button>
        <button
          class="dialog-button confirm-button"
          :class="confirmButtonClass"
          @click="handleConfirm"
          :disabled="loading"
        >
          <span v-if="loading" class="loading-spinner"></span>
          {{ loading ? loadingText : confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'info', // info, success, warning, error, confirm
    validator: (value) => ['info', 'success', 'warning', 'error', 'confirm'].includes(value)
  },
  title: {
    type: String,
    default: '提示'
  },
  message: {
    type: String,
    required: true
  },
  confirmText: {
    type: String,
    default: '确定'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  showCancelButton: {
    type: Boolean,
    default: false
  },
  showCloseButton: {
    type: Boolean,
    default: true
  },
  closeOnClickOutside: {
    type: Boolean,
    default: false
  },
  inputType: {
    type: String,
    default: null // text, textarea, password, email 等
  },
  inputPlaceholder: {
    type: String,
    default: '请输入...'
  },
  loading: {
    type: Boolean,
    default: false
  },
  loadingText: {
    type: String,
    default: '处理中...'
  }
})

// Emits
const emit = defineEmits(['confirm', 'cancel', 'close', 'update:visible'])

// 响应式数据
const inputValue = ref('')

// 计算属性
const iconText = computed(() => {
  const iconMap = {
    info: 'ℹ️',
    success: '✅',
    warning: '⚠️',
    error: '❌',
    confirm: '❓'
  }
  return iconMap[props.type] || 'ℹ️'
})

const headerClass = computed(() => {
  return `dialog-header-${props.type}`
})

const confirmButtonClass = computed(() => {
  return `confirm-button-${props.type}`
})

// 方法
const handleConfirm = () => {
  const result = {
    confirmed: true,
    inputValue: inputValue.value
  }
  emit('confirm', result)
  emit('update:visible', false)
}

const handleCancel = () => {
  const result = {
    confirmed: false,
    inputValue: inputValue.value
  }
  emit('cancel', result)
  emit('update:visible', false)
}

const handleOverlayClick = () => {
  if (props.closeOnClickOutside) {
    handleCancel()
  }
}

// 监听visible变化，重置输入值
watch(() => props.visible, (newVal) => {
  if (newVal) {
    inputValue.value = ''
  }
})
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.dialog-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  min-width: 320px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.dialog-header {
  padding: 20px 24px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.dialog-header-info {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
}

.dialog-header-success {
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
}

.dialog-header-warning {
  background: linear-gradient(135deg, #fff3e0 0%, #fef7e0 100%);
}

.dialog-header-error {
  background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
}

.dialog-header-confirm {
  background: linear-gradient(135deg, #e1f5fe 0%, #f0f4c3 100%);
}

.dialog-icon {
  font-size: 24px;
  line-height: 1;
}

.dialog-title {
  flex: 1;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-button:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #666;
}

.dialog-content {
  padding: 24px;
}

.dialog-message {
  margin: 0 0 16px 0;
  font-size: 16px;
  line-height: 1.5;
  color: #555;
  white-space: pre-wrap;
}

.dialog-input {
  margin-top: 16px;
}

.input-field {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.input-field:focus {
  outline: none;
  border-color: #2196f3;
}

.textarea-field {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.dialog-actions {
  padding: 16px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.dialog-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 80px;
  justify-content: center;
}

.cancel-button {
  background: #f5f5f5;
  color: #666;
}

.cancel-button:hover {
  background: #e0e0e0;
}

.confirm-button {
  color: white;
}

.confirm-button-info {
  background: #2196f3;
}

.confirm-button-info:hover {
  background: #1976d2;
}

.confirm-button-success {
  background: #4caf50;
}

.confirm-button-success:hover {
  background: #388e3c;
}

.confirm-button-warning {
  background: #ff9800;
}

.confirm-button-warning:hover {
  background: #f57c00;
}

.confirm-button-error {
  background: #f44336;
}

.confirm-button-error:hover {
  background: #d32f2f;
}

.confirm-button-confirm {
  background: #2196f3;
}

.confirm-button-confirm:hover {
  background: #1976d2;
}

.confirm-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

/**
 * 网格JSON数据验证工具
 * 用于验证网格数据的格式和有效性
 */

/**
 * 验证网格JSON数据
 * @param {Object} gridData - 网格数据对象
 * @returns {Object} 验证结果 { isValid: boolean, errors: string[] }
 */
export function validateGridData(gridData) {
  const errors = [];

  try {
    // ==================== 基本信息验证 ====================
    
    // 验证网格名称
    if (!gridData.name || typeof gridData.name !== 'string') {
      errors.push('网格名称不能为空且必须为字符串');
    } else if (gridData.name.trim().length < 2) {
      errors.push('网格名称长度不能少于2个字符');
    } else if (gridData.name.length > 50) {
      errors.push('网格名称长度不能超过50个字符');
    }

    // 验证状态
    const validStatuses = ['active', 'inactive', 'pending', 'archived'];
    if (gridData.status && !validStatuses.includes(gridData.status)) {
      errors.push(`网格状态必须为以下值之一: ${validStatuses.join(', ')}`);
    }

    // 验证描述长度
    if (gridData.description && gridData.description.length > 500) {
      errors.push('网格描述长度不能超过500个字符');
    }

    // ==================== 几何信息验证 ====================
    
    if (!gridData.geometry) {
      errors.push('几何信息不能为空');
    } else {
      // 验证几何类型
      if (gridData.geometry.type !== 'Polygon') {
        errors.push('几何类型必须为 "Polygon"');
      }

      // 验证坐标数组
      if (!gridData.geometry.coordinates || !Array.isArray(gridData.geometry.coordinates)) {
        errors.push('坐标数组不能为空且必须为数组');
      } else {
        const coordinates = gridData.geometry.coordinates;
        
        // 验证外边界
        if (coordinates.length === 0 || !Array.isArray(coordinates[0])) {
          errors.push('外边界坐标不能为空且必须为数组');
        } else {
          const outerRing = coordinates[0];
          
          // 验证最少点数（只需要3个点即可构成多边形）
          if (outerRing.length < 3) {
            errors.push('多边形至少需要3个点');
          } else {
            // 验证每个坐标点
            outerRing.forEach((point, index) => {
              if (!Array.isArray(point) || point.length !== 2) {
                errors.push(`第${index + 1}个坐标点格式错误，应为[经度, 纬度]`);
              } else {
                const [lng, lat] = point;

                // 验证经度范围
                if (typeof lng !== 'number' || lng < -180 || lng > 180) {
                  errors.push(`第${index + 1}个点的经度无效，应在-180到180之间`);
                }

                // 验证纬度范围
                if (typeof lat !== 'number' || lat < -90 || lat > 90) {
                  errors.push(`第${index + 1}个点的纬度无效，应在-90到90之间`);
                }
              }
            });

            // 移除多边形闭合验证 - 不再要求第一个点和最后一个点相同

            // 验证是否自相交（简单检查）
            if (outerRing.length > 4 && hasSimpleSelfIntersection(outerRing)) {
              errors.push('多边形存在自相交，请检查坐标顺序');
            }
          }
        }
      }
    }

    // ==================== 属性信息验证 ====================
    
    if (gridData.properties) {
      const props = gridData.properties;
      
      // 验证面积
      if (props.area !== undefined && (typeof props.area !== 'number' || props.area < 0)) {
        errors.push('面积必须为非负数');
      }
      
      // 验证周长
      if (props.perimeter !== undefined && (typeof props.perimeter !== 'number' || props.perimeter < 0)) {
        errors.push('周长必须为非负数');
      }
      
      // 验证中心点
      if (props.centerPoint) {
        const center = props.centerPoint;
        if (typeof center.longitude !== 'number' || center.longitude < -180 || center.longitude > 180) {
          errors.push('中心点经度无效');
        }
        if (typeof center.latitude !== 'number' || center.latitude < -90 || center.latitude > 90) {
          errors.push('中心点纬度无效');
        }
      }
    }

    // ==================== 元数据验证 ====================
    
    if (gridData.metadata) {
      const meta = gridData.metadata;
      
      // 验证创建时间格式
      if (meta.createdAt && !isValidISODate(meta.createdAt)) {
        errors.push('创建时间格式无效，应为ISO 8601格式');
      }
      
      // 验证数据来源
      const validSources = ['manual_drawing', 'import_shapefile', 'import_geojson', 'import_excel', 'api_creation', 'batch_generation'];
      if (meta.source && !validSources.includes(meta.source)) {
        errors.push(`数据来源必须为以下值之一: ${validSources.join(', ')}`);
      }
      
      // 验证版本号格式
      if (meta.version && !/^\d+\.\d+(\.\d+)?$/.test(meta.version)) {
        errors.push('版本号格式无效，应为 x.y 或 x.y.z 格式');
      }
    }

    // ==================== 业务数据验证 ====================
    
    if (gridData.businessData) {
      const business = gridData.businessData;
      const numericFields = ['households', 'population', 'buildings', 'events', 'patrols', 'complaints'];
      
      numericFields.forEach(field => {
        if (business[field] !== undefined && (typeof business[field] !== 'number' || business[field] < 0)) {
          errors.push(`${field}必须为非负整数`);
        }
      });
      
      // 验证满意度评分
      if (business.satisfaction !== undefined && 
          (typeof business.satisfaction !== 'number' || business.satisfaction < 0 || business.satisfaction > 100)) {
        errors.push('满意度评分必须为0-100之间的数值');
      }
    }

  } catch (error) {
    errors.push(`数据验证过程中发生错误: ${error.message}`);
  }

  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

/**
 * 验证ISO 8601日期格式
 * @param {string} dateString - 日期字符串
 * @returns {boolean} 是否为有效的ISO日期
 */
function isValidISODate(dateString) {
  try {
    const date = new Date(dateString);
    return date.toISOString() === dateString;
  } catch {
    return false;
  }
}

/**
 * 简单的自相交检测
 * @param {Array} ring - 坐标环
 * @returns {boolean} 是否存在自相交
 */
function hasSimpleSelfIntersection(ring) {
  // 简化版本：检查相邻边是否相交
  // 实际应用中建议使用专业的几何库
  for (let i = 0; i < ring.length - 3; i++) {
    for (let j = i + 2; j < ring.length - 1; j++) {
      if (j === ring.length - 2 && i === 0) continue; // 跳过首尾相连的情况
      
      const line1 = [ring[i], ring[i + 1]];
      const line2 = [ring[j], ring[j + 1]];
      
      if (doLinesIntersect(line1[0], line1[1], line2[0], line2[1])) {
        return true;
      }
    }
  }
  return false;
}

/**
 * 检测两条线段是否相交
 * @param {Array} p1 - 线段1起点
 * @param {Array} p2 - 线段1终点
 * @param {Array} p3 - 线段2起点
 * @param {Array} p4 - 线段2终点
 * @returns {boolean} 是否相交
 */
function doLinesIntersect(p1, p2, p3, p4) {
  const det = (p2[0] - p1[0]) * (p4[1] - p3[1]) - (p4[0] - p3[0]) * (p2[1] - p1[1]);
  if (det === 0) return false; // 平行线
  
  const lambda = ((p4[1] - p3[1]) * (p4[0] - p1[0]) + (p3[0] - p4[0]) * (p4[1] - p1[1])) / det;
  const gamma = ((p1[1] - p2[1]) * (p4[0] - p1[0]) + (p2[0] - p1[0]) * (p4[1] - p1[1])) / det;
  
  return (0 < lambda && lambda < 1) && (0 < gamma && gamma < 1);
}

/**
 * 格式化验证错误信息
 * @param {Array} errors - 错误数组
 * @returns {string} 格式化的错误信息
 */
export function formatValidationErrors(errors) {
  if (errors.length === 0) {
    return '数据验证通过';
  }
  
  return `数据验证失败，发现 ${errors.length} 个错误：\n${errors.map((error, index) => `${index + 1}. ${error}`).join('\n')}`;
}

/**
 * 验证并格式化网格数据
 * @param {Object} gridData - 网格数据
 * @returns {Object} 验证和格式化结果
 */
export function validateAndFormatGridData(gridData) {
  const validation = validateGridData(gridData);
  
  if (!validation.isValid) {
    return {
      success: false,
      message: formatValidationErrors(validation.errors),
      errors: validation.errors
    };
  }
  
  // 如果验证通过，进行数据格式化
  const formattedData = {
    ...gridData,
    // 确保必要字段存在
    status: gridData.status || 'active',
    description: gridData.description || '',
    metadata: {
      ...gridData.metadata,
      coordinateSystem: gridData.metadata?.coordinateSystem || 'WGS84',
      version: gridData.metadata?.version || '1.0'
    },
    businessData: {
      households: 0,
      population: 0,
      buildings: 0,
      events: 0,
      patrols: 0,
      complaints: 0,
      satisfaction: 0,
      ...gridData.businessData
    }
  };
  
  return {
    success: true,
    message: '数据验证通过',
    data: formattedData
  };
}

<template>
  <div id="app-layout">
    <!-- 简化的布局，主要用于登录和注册页面 -->
    <main>
      <router-view />
    </main>

    <!-- 版权栏已隐藏 -->
  </div>
</template>

<script setup>
import { computed } from 'vue';
import <PERSON><PERSON><PERSON>ogo from '../components/GongdaLogo.vue';

// 这个布局现在主要用于登录和注册页面
// 主要的应用布局已经移到 SidebarLayout.vue

// 计算当前年份
const currentYear = computed(() => new Date().getFullYear());
</script>

<style scoped>
#app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* 版权栏样式已移除 */
</style>
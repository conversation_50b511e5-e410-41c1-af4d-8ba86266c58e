<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>退出登录调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .debug-section h3 {
            color: #2196F3;
            margin-top: 0;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
        .log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            color: #ff6b6b;
        }
        .success {
            color: #51cf66;
        }
        .info {
            color: #74c0fc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 退出登录调试工具</h1>
        
        <div class="debug-section">
            <h3>问题分析</h3>
            <p>根据控制台错误，问题出现在dialog.js的渲染函数中。让我们逐步调试：</p>
            <ul>
                <li>ReferenceError: handleConfirm is not defined</li>
                <li>Unhandled error during execution of render function</li>
                <li>Promise.then 错误</li>
            </ul>
        </div>

        <div class="debug-section">
            <h3>调试步骤</h3>
            <button onclick="testBasicConfirm()">1. 测试原生confirm</button>
            <button onclick="testConsoleLog()">2. 测试console.log</button>
            <button onclick="testLocalStorage()">3. 测试localStorage清理</button>
            <button onclick="testFullLogout()">4. 测试完整退出流程</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="debug-section">
            <h3>调试日志</h3>
            <div id="debugLog" class="log">
                <div class="info">调试工具已准备就绪...</div>
            </div>
        </div>

        <div class="debug-section">
            <h3>建议的修复方案</h3>
            <p>基于错误分析，建议采用以下修复方案：</p>
            <ol>
                <li><strong>暂时禁用自定义dialog</strong> - 直接使用原生confirm</li>
                <li><strong>简化退出登录逻辑</strong> - 减少复杂的异步操作</li>
                <li><strong>添加详细的错误日志</strong> - 便于调试</li>
                <li><strong>确保store方法正常工作</strong> - 验证authStore和userStore</li>
            </ol>
        </div>

        <div class="debug-section">
            <h3>修复代码建议</h3>
            <pre style="background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto;">
// 简化的退出登录代码
const logout = async () => {
  console.log('🚪 开始退出登录流程');
  
  // 使用原生confirm
  const confirmed = confirm('确定要退出登录吗？');
  
  if (confirmed) {
    try {
      console.log('✅ 用户确认退出');
      
      // 清理localStorage
      localStorage.removeItem('userToken');
      localStorage.removeItem('userType');
      localStorage.removeItem('userData');
      localStorage.removeItem('auth_data');
      
      console.log('✅ localStorage已清理');
      
      // 跳转到登录页
      window.location.href = '/login';
      
    } catch (error) {
      console.error('❌ 退出登录失败:', error);
      alert('退出登录失败: ' + error.message);
    }
  } else {
    console.log('❌ 用户取消退出');
  }
};
            </pre>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function testBasicConfirm() {
            log('测试原生confirm...', 'info');
            try {
                const result = confirm('这是一个测试确认框，点击确定继续');
                log(`原生confirm结果: ${result}`, result ? 'success' : 'error');
            } catch (error) {
                log(`原生confirm失败: ${error.message}`, 'error');
            }
        }

        function testConsoleLog() {
            log('测试console.log...', 'info');
            try {
                console.log('这是一个测试日志');
                log('console.log正常工作', 'success');
            } catch (error) {
                log(`console.log失败: ${error.message}`, 'error');
            }
        }

        function testLocalStorage() {
            log('测试localStorage操作...', 'info');
            try {
                // 设置测试数据
                localStorage.setItem('test_key', 'test_value');
                log('localStorage.setItem 成功', 'success');
                
                // 读取测试数据
                const value = localStorage.getItem('test_key');
                log(`localStorage.getItem 结果: ${value}`, 'success');
                
                // 删除测试数据
                localStorage.removeItem('test_key');
                log('localStorage.removeItem 成功', 'success');
                
                // 验证删除
                const deletedValue = localStorage.getItem('test_key');
                log(`删除后的值: ${deletedValue}`, deletedValue === null ? 'success' : 'error');
                
            } catch (error) {
                log(`localStorage操作失败: ${error.message}`, 'error');
            }
        }

        function testFullLogout() {
            log('测试完整退出登录流程...', 'info');
            
            try {
                // 1. 确认对话框
                log('步骤1: 显示确认对话框', 'info');
                const confirmed = confirm('确定要测试退出登录吗？');
                
                if (confirmed) {
                    log('用户确认退出', 'success');
                    
                    // 2. 清理数据
                    log('步骤2: 清理localStorage数据', 'info');
                    const keysToRemove = ['userToken', 'userType', 'userData', 'auth_data'];
                    
                    keysToRemove.forEach(key => {
                        localStorage.removeItem(key);
                        log(`已删除: ${key}`, 'success');
                    });
                    
                    // 3. 验证清理结果
                    log('步骤3: 验证清理结果', 'info');
                    const remainingKeys = keysToRemove.filter(key => localStorage.getItem(key) !== null);
                    
                    if (remainingKeys.length === 0) {
                        log('✅ 所有数据已清理完成', 'success');
                    } else {
                        log(`⚠️ 仍有数据残留: ${remainingKeys.join(', ')}`, 'error');
                    }
                    
                    // 4. 模拟跳转
                    log('步骤4: 模拟页面跳转', 'info');
                    log('正常情况下会跳转到 /login', 'success');
                    
                    log('🎉 完整退出登录流程测试完成', 'success');
                    
                } else {
                    log('用户取消退出', 'error');
                }
                
            } catch (error) {
                log(`完整流程测试失败: ${error.message}`, 'error');
            }
        }

        function clearLog() {
            const logElement = document.getElementById('debugLog');
            logElement.innerHTML = '<div class="info">日志已清空...</div>';
        }

        // 页面加载时的初始化
        window.onload = function() {
            log('调试工具初始化完成', 'success');
            log('请按顺序执行测试步骤', 'info');
        };
    </script>
</body>
</html>

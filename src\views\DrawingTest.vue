<template>
  <div class="drawing-test">
    <div class="test-header">
      <h1>地图绘制功能测试</h1>
      <div class="test-controls">
        <button 
          :class="['test-btn', { active: isDrawing }]"
          @click="toggleDrawing"
        >
          {{ isDrawing ? '停止绘制' : '开始绘制' }}
        </button>
        <button class="test-btn" @click="clearAll">清除所有</button>
        <button class="test-btn" @click="testBasicClick">测试基础点击</button>
      </div>
    </div>
    
    <div class="test-status">
      <div class="status-item">
        <strong>绘制状态:</strong> {{ isDrawing ? '激活' : '未激活' }}
      </div>
      <div class="status-item">
        <strong>标记数量:</strong> {{ pointCount }}
      </div>
      <div class="status-item">
        <strong>地图状态:</strong> {{ mapStatus }}
      </div>
    </div>
    
    <div class="map-container">
      <MapComponent
        ref="mapComponent"
        map-id="drawing-test-map"
        :height="'400px'"
        :center="[116.4074, 39.9042]"
        :zoom="15"
        :markers="[]"
        :polygons="[]"
        :show-controls="true"
        :show-drawing-tools="false"
        @map-ready="onMapReady"
      />
    </div>
    
    <div class="test-log">
      <h3>测试日志</h3>
      <div class="log-content">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
      <button class="clear-log-btn" @click="clearLogs">清除日志</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import MapComponent from '../components/MapComponent.vue';

// 响应式数据
const mapComponent = ref(null);
const isDrawing = ref(false);
const pointCount = ref(0);
const logs = ref([]);
const tempMarkers = ref([]);
const mapStatus = ref('未初始化');

// 添加日志
const addLog = (message) => {
  const timestamp = new Date().toLocaleTimeString();
  logs.value.unshift(`[${timestamp}] ${message}`);
  if (logs.value.length > 50) {
    logs.value.pop();
  }
  console.log(`[绘制测试] ${message}`);
};

// 地图就绪事件
const onMapReady = (map) => {
  mapStatus.value = '已初始化';
  addLog('✅ 地图初始化完成');
  addLog(`地图实例类型: ${map.constructor.name}`);
  
  // 测试地图基本功能
  setTimeout(() => {
    testMapInstance();
  }, 1000);
};

// 测试地图实例
const testMapInstance = () => {
  const mapInstance = mapComponent.value?.getMap();
  if (mapInstance) {
    addLog('✅ 地图实例访问成功');
    addLog(`地图中心: ${mapInstance.getCenter()}`);
    addLog(`地图缩放: ${mapInstance.getZoom()}`);
  } else {
    addLog('❌ 无法访问地图实例');
  }
};

// 测试基础点击功能
const testBasicClick = () => {
  addLog('🧪 开始测试基础点击功能...');
  
  const mapInstance = mapComponent.value?.getMap();
  if (!mapInstance) {
    addLog('❌ 地图实例不可用');
    return;
  }
  
  // 添加一次性点击监听器
  const testClickHandler = (event) => {
    addLog(`✅ 基础点击测试成功: ${event.lnglat.lng}, ${event.lnglat.lat}`);
    mapInstance.off('click', testClickHandler);
  };
  
  mapInstance.on('click', testClickHandler);
  addLog('👆 请点击地图进行基础点击测试...');
};

// 切换绘制模式
const toggleDrawing = () => {
  isDrawing.value = !isDrawing.value;
  
  if (isDrawing.value) {
    startDrawing();
  } else {
    stopDrawing();
  }
};

// 开始绘制
const startDrawing = () => {
  addLog('🎯 启动绘制模式...');
  
  const mapInstance = mapComponent.value?.getMap();
  if (!mapInstance) {
    addLog('❌ 无法获取地图实例');
    isDrawing.value = false;
    return;
  }
  
  addLog('✅ 地图实例获取成功');
  
  try {
    // 禁用地图拖拽
    mapInstance.setStatus({
      dragEnable: false,
      doubleClickZoom: false
    });
    addLog('✅ 地图拖拽已禁用');
    
    // 改变鼠标样式
    const mapContainer = document.getElementById('drawing-test-map');
    if (mapContainer) {
      mapContainer.style.cursor = 'crosshair';
      addLog('✅ 鼠标样式已设置为十字光标');
    }
    
    // 绑定点击事件
    const clickHandler = (event) => {
      addLog(`🎯 绘制点击: ${event.lnglat.lng.toFixed(6)}, ${event.lnglat.lat.toFixed(6)}`);
      
      try {
        const { lng, lat } = event.lnglat;
        
        // 创建标记
        const marker = new AMap.Marker({
          position: [lng, lat],
          title: `绘制点 ${pointCount.value + 1}`
        });
        
        mapInstance.add(marker);
        tempMarkers.value.push(marker);
        pointCount.value++;
        
        addLog(`✅ 标记 ${pointCount.value} 创建成功`);
        
      } catch (error) {
        addLog(`❌ 创建标记失败: ${error.message}`);
      }
    };
    
    mapInstance.on('click', clickHandler);
    mapInstance.drawingClickHandler = clickHandler; // 存储引用
    
    addLog('✅ 点击事件监听器已绑定');
    addLog('👆 现在可以点击地图添加标记点');
    
  } catch (error) {
    addLog(`❌ 启动绘制模式失败: ${error.message}`);
    isDrawing.value = false;
  }
};

// 停止绘制
const stopDrawing = () => {
  addLog('🛑 停止绘制模式...');
  
  const mapInstance = mapComponent.value?.getMap();
  if (!mapInstance) return;
  
  try {
    // 恢复地图状态
    mapInstance.setStatus({
      dragEnable: true,
      doubleClickZoom: true
    });
    
    // 恢复鼠标样式
    const mapContainer = document.getElementById('drawing-test-map');
    if (mapContainer) {
      mapContainer.style.cursor = '';
    }
    
    // 移除事件监听
    if (mapInstance.drawingClickHandler) {
      mapInstance.off('click', mapInstance.drawingClickHandler);
      mapInstance.drawingClickHandler = null;
    }
    
    addLog('✅ 绘制模式已停止');
    
  } catch (error) {
    addLog(`❌ 停止绘制模式失败: ${error.message}`);
  }
};

// 清除所有标记
const clearAll = () => {
  const mapInstance = mapComponent.value?.getMap();
  if (!mapInstance) return;
  
  tempMarkers.value.forEach(marker => {
    mapInstance.remove(marker);
  });
  
  tempMarkers.value = [];
  pointCount.value = 0;
  
  addLog('🧹 已清除所有标记点');
};

// 清除日志
const clearLogs = () => {
  logs.value = [];
};

onMounted(() => {
  addLog('🚀 绘制测试页面已加载');
});
</script>

<style scoped>
.drawing-test {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.test-header {
  background: #4a90e2;
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-header h1 {
  margin: 0;
  font-size: 1.3em;
}

.test-controls {
  display: flex;
  gap: 8px;
}

.test-btn {
  background: white;
  color: #4a90e2;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.test-btn:hover {
  background: #f0f7ff;
}

.test-btn.active {
  background: #ff6b35;
  color: white;
}

.test-status {
  background: white;
  padding: 10px 20px;
  border-bottom: 1px solid #e1e8ed;
  display: flex;
  gap: 20px;
}

.status-item {
  font-size: 14px;
}

.map-container {
  flex: 1;
  padding: 20px;
  min-height: 300px;
}

.test-log {
  background: white;
  border-top: 1px solid #e1e8ed;
  padding: 15px 20px;
  max-height: 200px;
  overflow-y: auto;
}

.test-log h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 14px;
}

.log-content {
  font-family: 'Courier New', monospace;
  font-size: 11px;
  max-height: 120px;
  overflow-y: auto;
}

.log-item {
  padding: 1px 0;
  color: #666;
  line-height: 1.3;
}

.clear-log-btn {
  margin-top: 10px;
  padding: 4px 8px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
}
</style>

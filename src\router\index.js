/**
 * Vue Router 配置 - 备用文件，不应该被使用
 * 包含认证路由和权限控制
 * 注意：这个文件不应该被使用，请使用 router.js
 */

// 这个文件被禁用，请使用 router.js
throw new Error('不应该使用 router/index.js，请使用 router.js');

import { createRouter, createWebHistory } from 'vue-router'

// 导入组件
import LoginForm from '../components/LoginForm.vue'
import Dashboard from '../views/Dashboard.vue'
import GridManagement from '../views/GridManagement.vue'
import GISManagement from '../views/GISManagement.vue'

// 路由配置
const routes = [
  // 根路径重定向
  {
    path: '/',
    redirect: '/login'  // 修改为跳转到登录页
  },
  
  // 登录页面
  {
    path: '/login',
    name: 'Login',
    component: LoginForm,
    meta: {
      title: '用户登录',
      requiresAuth: false,
      layout: 'auth' // 使用认证布局
    }
  },
  
  // 仪表板
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '仪表板',
      requiresAuth: true,
      permissions: ['dashboard:view']
    }
  },
  
  // 物业管理相关路由
  {
    path: '/property',
    name: 'Property',
    meta: {
      title: '物业管理',
      requiresAuth: true
    },
    children: [
      // 网格管理
      {
        path: 'gis-grid',
        name: 'GridManagement',
        component: GridManagement,
        meta: {
          title: '网格管理',
          requiresAuth: true,
          permissions: ['grid:view']
        }
      },
      
      // GIS管理
      {
        path: 'gis-management',
        name: 'GISManagement',
        component: GISManagement,
        meta: {
          title: 'GIS管理',
          requiresAuth: true,
          permissions: ['gis:view', 'grid:create', 'grid:edit']
        }
      }
    ]
  },
  
  // 用户管理
  {
    path: '/users',
    name: 'UserManagement',
    component: () => import('../views/UserManagement.vue'),
    meta: {
      title: '用户管理',
      requiresAuth: true,
      permissions: ['user:view'],
      roles: ['admin', 'manager']
    }
  },
  
  // 系统设置
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('../views/Settings.vue'),
    meta: {
      title: '系统设置',
      requiresAuth: true,
      permissions: ['settings:view'],
      roles: ['admin']
    }
  },
  
  // 个人中心
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../views/Profile.vue'),
    meta: {
      title: '个人中心',
      requiresAuth: true
    }
  },

  // 弹窗演示页面
  {
    path: '/dialog-demo',
    name: 'DialogDemo',
    component: () => import('../views/DialogDemo.vue'),
    meta: {
      title: '弹窗演示',
      requiresAuth: true
    }
  },

  // 居民端首页
  {
    path: '/resident',
    name: 'ResidentHome',
    component: () => import('../views/ResidentHome.vue'),
    meta: {
      title: '居民端',
      requiresAuth: true,
      userType: 1 // 仅居民可访问
    }
  },

  // 物业端首页（网格员和物业管理员）
  {
    path: '/property-home',
    name: 'PropertyHome',
    component: () => import('../views/PropertyHome.vue'),
    meta: {
      title: '物业端',
      requiresAuth: true,
      userType: [2, 3] // 网格员和管理员可访问
    }
  },
  
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue'),
    meta: {
      title: '页面不存在',
      requiresAuth: false
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  
  // 滚动行为
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 导出路由实例
export default router

// 导出路由配置供其他地方使用
export { routes }

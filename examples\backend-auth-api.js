/**
 * 后端认证API完整示例
 * 基于Express.js + JWT + MongoDB
 */

const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');

const app = express();

// ==================== 配置 ====================

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m'; // 15分钟
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d'; // 7天

// ==================== 中间件 ====================

app.use(express.json());

// 登录频率限制
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 最多5次登录尝试
  message: {
    success: false,
    message: '登录尝试过于频繁，请15分钟后再试',
    error: {
      code: 'TOO_MANY_ATTEMPTS',
      details: '15分钟内最多允许5次登录尝试'
    }
  },
  standardHeaders: true,
  legacyHeaders: false
});

// ==================== 数据库模型 ====================

// 用户模型
const UserSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 50
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  role: {
    type: String,
    enum: ['admin', 'manager', 'user'],
    default: 'user'
  },
  permissions: [{
    type: String
  }],
  status: {
    type: String,
    enum: ['active', 'inactive', 'locked'],
    default: 'active'
  },
  lastLoginAt: Date,
  loginAttempts: {
    type: Number,
    default: 0
  },
  lockUntil: Date
}, {
  timestamps: true
});

// 密码加密中间件
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// 密码验证方法
UserSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// 检查账户是否被锁定
UserSchema.methods.isLocked = function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
};

// 增加登录尝试次数
UserSchema.methods.incLoginAttempts = function() {
  // 如果之前有锁定且已过期，重置计数器
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }
  
  const updates = { $inc: { loginAttempts: 1 } };
  
  // 如果达到最大尝试次数且未锁定，则锁定账户
  if (this.loginAttempts + 1 >= 5 && !this.isLocked()) {
    updates.$set = {
      lockUntil: Date.now() + 2 * 60 * 60 * 1000 // 锁定2小时
    };
  }
  
  return this.updateOne(updates);
};

// 重置登录尝试次数
UserSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 },
    $set: { lastLoginAt: new Date() }
  });
};

const User = mongoose.model('User', UserSchema);

// 刷新Token模型
const RefreshTokenSchema = new mongoose.Schema({
  token: {
    type: String,
    required: true,
    unique: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  expiresAt: {
    type: Date,
    required: true
  },
  isRevoked: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// 自动删除过期的刷新token
RefreshTokenSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

const RefreshToken = mongoose.model('RefreshToken', RefreshTokenSchema);

// ==================== 工具函数 ====================

/**
 * 生成JWT token
 * @param {Object} payload - token载荷
 * @param {string} secret - 密钥
 * @param {string} expiresIn - 过期时间
 * @returns {string} JWT token
 */
function generateToken(payload, secret, expiresIn) {
  return jwt.sign(payload, secret, { expiresIn });
}

/**
 * 验证JWT token
 * @param {string} token - JWT token
 * @param {string} secret - 密钥
 * @returns {Object} 解码后的载荷
 */
function verifyToken(token, secret) {
  return jwt.verify(token, secret);
}

/**
 * 生成刷新token
 * @param {string} userId - 用户ID
 * @returns {Promise<string>} 刷新token
 */
async function generateRefreshToken(userId) {
  const token = jwt.sign({ userId }, JWT_REFRESH_SECRET, { expiresIn: JWT_REFRESH_EXPIRES_IN });
  
  // 计算过期时间
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + 7); // 7天后过期
  
  // 保存到数据库
  const refreshToken = new RefreshToken({
    token,
    userId,
    expiresAt
  });
  
  await refreshToken.save();
  return token;
}

/**
 * 认证中间件
 */
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
  
  if (!token) {
    return res.status(401).json({
      success: false,
      message: '缺少访问令牌',
      error: {
        code: 'MISSING_TOKEN',
        details: '请在请求头中提供Authorization令牌'
      }
    });
  }
  
  try {
    const decoded = verifyToken(token, JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    console.error('Token验证失败:', error);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '访问令牌已过期',
        error: {
          code: 'TOKEN_EXPIRED',
          details: '请使用刷新令牌获取新的访问令牌'
        }
      });
    } else if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: '无效的访问令牌',
        error: {
          code: 'INVALID_TOKEN',
          details: '提供的令牌格式错误或已被篡改'
        }
      });
    } else {
      return res.status(401).json({
        success: false,
        message: '令牌验证失败',
        error: {
          code: 'TOKEN_VERIFICATION_FAILED',
          details: error.message
        }
      });
    }
  }
}

// ==================== 认证路由 ====================

/**
 * 用户登录
 */
app.post('/api/auth/login',
  loginLimiter,
  [
    body('username')
      .notEmpty()
      .withMessage('用户名不能为空')
      .isLength({ min: 3, max: 50 })
      .withMessage('用户名长度必须在3-50个字符之间'),
    body('password')
      .notEmpty()
      .withMessage('密码不能为空')
      .isLength({ min: 6 })
      .withMessage('密码长度至少6个字符')
  ],
  async (req, res) => {
    const requestId = req.headers['x-request-id'] || `login_${Date.now()}`;
    
    try {
      console.log(`🔐 [${requestId}] 收到登录请求`);
      
      // 验证请求数据
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log(`❌ [${requestId}] 数据验证失败:`, errors.array());
        return res.status(422).json({
          success: false,
          message: '数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            details: errors.array().map(err => `${err.param}: ${err.msg}`).join('; '),
            fields: errors.array().map(err => err.param)
          }
        });
      }
      
      const { username, password } = req.body;
      console.log(`👤 [${requestId}] 用户名: ${username}`);
      
      // 查找用户
      const user = await User.findOne({ username }).select('+password');
      if (!user) {
        console.log(`❌ [${requestId}] 用户不存在: ${username}`);
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误',
          error: {
            code: 'INVALID_CREDENTIALS',
            details: '提供的用户名或密码不正确'
          }
        });
      }
      
      // 检查账户状态
      if (user.status === 'inactive') {
        console.log(`❌ [${requestId}] 账户已禁用: ${username}`);
        return res.status(403).json({
          success: false,
          message: '账户已被禁用',
          error: {
            code: 'ACCOUNT_DISABLED',
            details: '该账户已被管理员禁用，请联系管理员'
          }
        });
      }
      
      // 检查账户是否被锁定
      if (user.isLocked()) {
        console.log(`❌ [${requestId}] 账户已锁定: ${username}`);
        return res.status(423).json({
          success: false,
          message: '账户已被锁定',
          error: {
            code: 'ACCOUNT_LOCKED',
            details: '由于多次登录失败，账户已被临时锁定',
            lockUntil: user.lockUntil
          }
        });
      }
      
      // 验证密码
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        console.log(`❌ [${requestId}] 密码错误: ${username}`);
        
        // 增加登录尝试次数
        await user.incLoginAttempts();
        
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误',
          error: {
            code: 'INVALID_CREDENTIALS',
            details: '提供的用户名或密码不正确',
            remainingAttempts: Math.max(0, 5 - (user.loginAttempts + 1))
          }
        });
      }
      
      // 登录成功，重置登录尝试次数
      await user.resetLoginAttempts();
      
      console.log(`✅ [${requestId}] 登录成功: ${username}`);
      
      // 生成token
      const tokenPayload = {
        userId: user._id,
        username: user.username,
        role: user.role,
        permissions: user.permissions
      };
      
      const accessToken = generateToken(tokenPayload, JWT_SECRET, JWT_EXPIRES_IN);
      const refreshToken = await generateRefreshToken(user._id);
      
      // 计算过期时间
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 15); // 15分钟后过期
      
      console.log(`🔑 [${requestId}] Token生成成功`);
      
      // 返回成功响应
      res.json({
        success: true,
        message: '登录成功',
        data: {
          user: {
            id: user._id,
            username: user.username,
            email: user.email,
            role: user.role,
            permissions: user.permissions,
            lastLoginAt: user.lastLoginAt
          },
          token: accessToken,
          refreshToken: refreshToken,
          expiresAt: expiresAt.toISOString(),
          tokenType: 'Bearer'
        }
      });
      
    } catch (error) {
      console.error(`❌ [${requestId}] 登录处理失败:`, error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: {
          code: 'INTERNAL_ERROR',
          details: '登录过程中发生未知错误',
          requestId: requestId
        }
      });
    }
  }
);

/**
 * 刷新访问token
 */
app.post('/api/auth/refresh', async (req, res) => {
  const requestId = req.headers['x-request-id'] || `refresh_${Date.now()}`;
  
  try {
    console.log(`🔄 [${requestId}] 收到token刷新请求`);
    
    const { refreshToken } = req.body;
    
    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        message: '缺少刷新令牌',
        error: {
          code: 'MISSING_REFRESH_TOKEN',
          details: '请提供有效的刷新令牌'
        }
      });
    }
    
    // 验证刷新token
    let decoded;
    try {
      decoded = verifyToken(refreshToken, JWT_REFRESH_SECRET);
    } catch (error) {
      console.log(`❌ [${requestId}] 刷新token验证失败:`, error.message);
      return res.status(401).json({
        success: false,
        message: '无效的刷新令牌',
        error: {
          code: 'INVALID_REFRESH_TOKEN',
          details: '刷新令牌无效或已过期'
        }
      });
    }
    
    // 检查刷新token是否在数据库中存在且未被撤销
    const storedToken = await RefreshToken.findOne({
      token: refreshToken,
      userId: decoded.userId,
      isRevoked: false
    });
    
    if (!storedToken) {
      console.log(`❌ [${requestId}] 刷新token不存在或已撤销`);
      return res.status(401).json({
        success: false,
        message: '刷新令牌已失效',
        error: {
          code: 'REFRESH_TOKEN_REVOKED',
          details: '刷新令牌已被撤销或不存在'
        }
      });
    }
    
    // 获取用户信息
    const user = await User.findById(decoded.userId);
    if (!user || user.status !== 'active') {
      console.log(`❌ [${requestId}] 用户不存在或已禁用`);
      return res.status(401).json({
        success: false,
        message: '用户账户异常',
        error: {
          code: 'USER_ACCOUNT_ERROR',
          details: '用户不存在或账户已被禁用'
        }
      });
    }
    
    // 生成新的访问token
    const tokenPayload = {
      userId: user._id,
      username: user.username,
      role: user.role,
      permissions: user.permissions
    };
    
    const newAccessToken = generateToken(tokenPayload, JWT_SECRET, JWT_EXPIRES_IN);
    
    // 计算新的过期时间
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 15);
    
    console.log(`✅ [${requestId}] Token刷新成功`);
    
    res.json({
      success: true,
      message: 'Token刷新成功',
      data: {
        token: newAccessToken,
        expiresAt: expiresAt.toISOString(),
        tokenType: 'Bearer'
      }
    });
    
  } catch (error) {
    console.error(`❌ [${requestId}] Token刷新失败:`, error);
    
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: {
        code: 'INTERNAL_ERROR',
        details: 'Token刷新过程中发生未知错误',
        requestId: requestId
      }
    });
  }
});

/**
 * 用户登出
 */
app.post('/api/auth/logout', authenticateToken, async (req, res) => {
  const requestId = req.headers['x-request-id'] || `logout_${Date.now()}`;
  
  try {
    console.log(`🚪 [${requestId}] 收到登出请求，用户: ${req.user.username}`);
    
    // 撤销所有该用户的刷新token
    await RefreshToken.updateMany(
      { userId: req.user.userId, isRevoked: false },
      { isRevoked: true }
    );
    
    console.log(`✅ [${requestId}] 登出成功`);
    
    res.json({
      success: true,
      message: '登出成功',
      data: null
    });
    
  } catch (error) {
    console.error(`❌ [${requestId}] 登出失败:`, error);
    
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: {
        code: 'INTERNAL_ERROR',
        details: '登出过程中发生未知错误',
        requestId: requestId
      }
    });
  }
});

/**
 * 获取当前用户信息
 */
app.get('/api/auth/me', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId).select('-password');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在',
        error: {
          code: 'USER_NOT_FOUND',
          details: '当前用户不存在'
        }
      });
    }
    
    res.json({
      success: true,
      message: '获取用户信息成功',
      data: {
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          role: user.role,
          permissions: user.permissions,
          status: user.status,
          lastLoginAt: user.lastLoginAt,
          createdAt: user.createdAt
        }
      }
    });
    
  } catch (error) {
    console.error('获取用户信息失败:', error);
    
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: {
        code: 'INTERNAL_ERROR',
        details: '获取用户信息时发生错误'
      }
    });
  }
});

/**
 * 验证token有效性
 */
app.post('/api/auth/validate', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: 'Token有效',
    data: {
      user: {
        id: req.user.userId,
        username: req.user.username,
        role: req.user.role,
        permissions: req.user.permissions
      },
      isValid: true
    }
  });
});

// ==================== 启动服务器 ====================

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log(`🚀 认证服务器启动成功，端口: ${PORT}`);
  console.log(`🔐 登录接口: http://localhost:${PORT}/api/auth/login`);
  console.log(`🔄 刷新接口: http://localhost:${PORT}/api/auth/refresh`);
  console.log(`🚪 登出接口: http://localhost:${PORT}/api/auth/logout`);
});

module.exports = app;

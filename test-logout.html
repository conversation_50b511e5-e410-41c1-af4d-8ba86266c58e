<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>退出登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2196F3;
            margin-top: 0;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #2196F3;
        }
        .error {
            border-left-color: #f44336;
            background: #ffebee;
        }
        .success {
            border-left-color: #4caf50;
            background: #e8f5e8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 退出登录功能测试</h1>
        
        <div class="test-section">
            <h3>1. 模拟登录状态</h3>
            <p>首先设置一些模拟的登录数据到localStorage</p>
            <button onclick="simulateLogin()">🔑 模拟登录</button>
            <button onclick="checkLoginStatus()">📊 检查登录状态</button>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 测试退出登录</h3>
            <p>测试清除认证数据的功能</p>
            <button onclick="testLogout()">🚪 测试退出登录</button>
            <button onclick="testAuthStoreLogout()">🔐 测试AuthStore退出</button>
            <div id="logoutResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 验证清理结果</h3>
            <p>检查localStorage是否被正确清理</p>
            <button onclick="verifyCleanup()">🧹 验证清理结果</button>
            <div id="cleanupResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 自定义弹窗测试</h3>
            <p>测试自定义弹窗是否正常工作</p>
            <button onclick="testCustomDialog()">💬 测试自定义弹窗</button>
            <div id="dialogResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 模拟登录
        function simulateLogin() {
            const userData = {
                id: 1,
                username: 'testuser',
                userType: 2,
                email: '<EMAIL>'
            };
            
            localStorage.setItem('userToken', 'mock-token-12345');
            localStorage.setItem('userType', '2');
            localStorage.setItem('userData', JSON.stringify(userData));
            
            showResult('loginResult', '✅ 模拟登录成功！已设置用户数据到localStorage', 'success');
        }

        // 检查登录状态
        function checkLoginStatus() {
            const token = localStorage.getItem('userToken');
            const userType = localStorage.getItem('userType');
            const userData = localStorage.getItem('userData');
            
            const status = {
                token: token || '无',
                userType: userType || '无',
                userData: userData ? JSON.parse(userData) : '无'
            };
            
            showResult('loginResult', `📊 当前登录状态：\n${JSON.stringify(status, null, 2)}`, 'success');
        }

        // 测试退出登录（模拟用户存储的logout方法）
        function testLogout() {
            try {
                // 模拟userStore.logout()的行为
                localStorage.removeItem('userToken');
                localStorage.removeItem('userType');
                localStorage.removeItem('userData');
                
                showResult('logoutResult', '✅ UserStore退出登录成功！已清除localStorage数据', 'success');
            } catch (error) {
                showResult('logoutResult', `❌ 退出登录失败：${error.message}`, 'error');
            }
        }

        // 测试AuthStore退出登录
        function testAuthStoreLogout() {
            try {
                // 模拟authStore.logout()的行为
                // 清除认证相关的数据
                localStorage.removeItem('authToken');
                localStorage.removeItem('refreshToken');
                localStorage.removeItem('userInfo');
                localStorage.removeItem('userPermissions');
                localStorage.removeItem('userRole');
                
                // 也清除用户存储的数据
                localStorage.removeItem('userToken');
                localStorage.removeItem('userType');
                localStorage.removeItem('userData');
                
                showResult('logoutResult', '✅ AuthStore退出登录成功！已清除所有认证数据', 'success');
            } catch (error) {
                showResult('logoutResult', `❌ AuthStore退出登录失败：${error.message}`, 'error');
            }
        }

        // 验证清理结果
        function verifyCleanup() {
            const remainingKeys = [];
            const authKeys = ['userToken', 'userType', 'userData', 'authToken', 'refreshToken', 'userInfo', 'userPermissions', 'userRole'];
            
            authKeys.forEach(key => {
                if (localStorage.getItem(key)) {
                    remainingKeys.push(key);
                }
            });
            
            if (remainingKeys.length === 0) {
                showResult('cleanupResult', '✅ 清理验证成功！所有认证数据已被清除', 'success');
            } else {
                showResult('cleanupResult', `⚠️ 清理不完整！仍有以下数据残留：${remainingKeys.join(', ')}`, 'error');
            }
        }

        // 测试自定义弹窗
        function testCustomDialog() {
            // 由于这是独立的HTML文件，无法直接测试Vue组件
            // 但可以测试基本的confirm功能
            if (confirm('这是浏览器原生的confirm弹窗，点击确定继续测试')) {
                showResult('dialogResult', '✅ 浏览器原生弹窗测试成功！\n注意：在Vue应用中，这将被自定义弹窗替代', 'success');
            } else {
                showResult('dialogResult', '❌ 用户取消了弹窗操作', 'error');
            }
        }

        // 显示结果
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // 页面加载时检查状态
        window.onload = function() {
            checkLoginStatus();
        };
    </script>
</body>
</html>

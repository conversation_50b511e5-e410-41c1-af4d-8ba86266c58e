<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后端服务状态检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .response-data {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>后端服务状态检查工具</h1>
        
        <!-- Token输入 -->
        <div class="test-section">
            <h3>1. 设置认证Token</h3>
            <div class="input-group">
                <label for="tokenInput">JWT Token:</label>
                <input type="text" id="tokenInput" placeholder="请输入JWT Token（可选，用于需要认证的接口）">
            </div>
        </div>

        <!-- 基础连接测试 -->
        <div class="test-section">
            <h3>2. 基础连接测试</h3>
            <button class="test-button" onclick="testBasicConnection()">测试后端连接</button>
            <div id="basicStatus" class="status" style="display: none;"></div>
            <div id="basicResponse" class="response-data" style="display: none;"></div>
        </div>

        <!-- 投票API测试 -->
        <div class="test-section">
            <h3>3. 投票API测试</h3>
            <button class="test-button" onclick="testVoteListApi()">测试获取投票列表</button>
            <button class="test-button" onclick="testCreateVoteApi()">测试创建投票</button>
            <div id="voteStatus" class="status" style="display: none;"></div>
            <div id="voteResponse" class="response-data" style="display: none;"></div>
        </div>

        <!-- 其他API测试 -->
        <div class="test-section">
            <h3>4. 其他API测试</h3>
            <button class="test-button" onclick="testGridApi()">测试网格API</button>
            <button class="test-button" onclick="testIncidentApi()">测试事件API</button>
            <div id="otherStatus" class="status" style="display: none;"></div>
            <div id="otherResponse" class="response-data" style="display: none;"></div>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8080/api';

        function getToken() {
            return document.getElementById('tokenInput').value.trim();
        }

        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
        }

        function showResponse(elementId, data) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.style.display = 'block';
        }

        async function testBasicConnection() {
            showStatus('basicStatus', '正在测试连接...', 'info');
            
            try {
                const response = await fetch(`${BASE_URL}/vote/list`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(getToken() && { 'Authorization': `Bearer ${getToken()}` })
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    showStatus('basicStatus', `连接成功！状态码: ${response.status}`, 'success');
                } else {
                    showStatus('basicStatus', `连接失败！状态码: ${response.status}`, 'error');
                }
                
                showResponse('basicResponse', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                });
                
            } catch (error) {
                showStatus('basicStatus', `连接异常: ${error.message}`, 'error');
                showResponse('basicResponse', {
                    error: error.message,
                    type: error.name
                });
            }
        }

        async function testVoteListApi() {
            showStatus('voteStatus', '正在测试投票列表API...', 'info');
            
            try {
                const response = await fetch(`${BASE_URL}/vote/list`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(getToken() && { 'Authorization': `Bearer ${getToken()}` })
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    showStatus('voteStatus', `投票列表API正常！返回${data.data?.length || 0}个投票`, 'success');
                } else {
                    showStatus('voteStatus', `投票列表API失败！状态码: ${response.status}`, 'error');
                }
                
                showResponse('voteResponse', data);
                
            } catch (error) {
                showStatus('voteStatus', `投票列表API异常: ${error.message}`, 'error');
                showResponse('voteResponse', { error: error.message });
            }
        }

        async function testCreateVoteApi() {
            showStatus('voteStatus', '正在测试创建投票API...', 'info');
            
            const testVoteData = {
                title: "API测试投票",
                description: "这是一个API测试投票",
                startTime: "2025-07-03 00:00:00",
                endTime: "2025-07-23 00:00:00",
                maxChoices: 1,
                isAnonymous: 0,
                options: [
                    { sortOrder: 1, content: "选项1" },
                    { sortOrder: 2, content: "选项2" }
                ],
                scopes: [
                    { targetType: 1, targetId: 1 }
                ]
            };
            
            try {
                const response = await fetch(`${BASE_URL}/vote`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(getToken() && { 'Authorization': `Bearer ${getToken()}` })
                    },
                    body: JSON.stringify(testVoteData)
                });

                const data = await response.json();
                
                if (response.ok) {
                    showStatus('voteStatus', `创建投票API正常！`, 'success');
                } else {
                    showStatus('voteStatus', `创建投票API失败！状态码: ${response.status}`, 'error');
                }
                
                showResponse('voteResponse', data);
                
            } catch (error) {
                showStatus('voteStatus', `创建投票API异常: ${error.message}`, 'error');
                showResponse('voteResponse', { error: error.message });
            }
        }

        async function testGridApi() {
            showStatus('otherStatus', '正在测试网格API...', 'info');
            
            try {
                const response = await fetch(`${BASE_URL}/grids/communities`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(getToken() && { 'Authorization': `Bearer ${getToken()}` })
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    showStatus('otherStatus', `网格API正常！`, 'success');
                } else {
                    showStatus('otherStatus', `网格API失败！状态码: ${response.status}`, 'error');
                }
                
                showResponse('otherResponse', data);
                
            } catch (error) {
                showStatus('otherStatus', `网格API异常: ${error.message}`, 'error');
                showResponse('otherResponse', { error: error.message });
            }
        }

        async function testIncidentApi() {
            showStatus('otherStatus', '正在测试事件API...', 'info');
            
            try {
                const response = await fetch(`${BASE_URL}/incident/list/1`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(getToken() && { 'Authorization': `Bearer ${getToken()}` })
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    showStatus('otherStatus', `事件API正常！`, 'success');
                } else {
                    showStatus('otherStatus', `事件API失败！状态码: ${response.status}`, 'error');
                }
                
                showResponse('otherResponse', data);
                
            } catch (error) {
                showStatus('otherStatus', `事件API异常: ${error.message}`, 'error');
                showResponse('otherResponse', { error: error.message });
            }
        }

        // 页面加载时自动测试基础连接
        window.onload = function() {
            console.log('页面加载完成，可以开始测试');
        };
    </script>
</body>
</html>

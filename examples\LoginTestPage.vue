<template>
  <div class="login-test-page">
    <div class="container">
      <h1>登录和API测试页面</h1>
      
      <!-- 认证状态显示 -->
      <div class="auth-status">
        <h2>认证状态</h2>
        <div class="status-grid">
          <div class="status-item">
            <label>登录状态:</label>
            <span :class="['status', isAuthenticated ? 'success' : 'error']">
              {{ isAuthenticated ? '已登录' : '未登录' }}
            </span>
          </div>
          
          <div class="status-item" v-if="currentUser">
            <label>当前用户:</label>
            <span>{{ currentUser.username }} ({{ currentUser.role }})</span>
          </div>
          
          <div class="status-item" v-if="token">
            <label>Token:</label>
            <span class="token">{{ token.substring(0, 30) }}...</span>
          </div>
          
          <div class="status-item" v-if="tokenExpiry">
            <label>Token过期时间:</label>
            <span>{{ new Date(tokenExpiry).toLocaleString('zh-CN') }}</span>
          </div>
        </div>
      </div>

      <!-- 登录测试 -->
      <div class="test-section">
        <h2>登录测试</h2>
        <div class="login-test">
          <div class="form-group">
            <label>用户名:</label>
            <input v-model="testCredentials.username" type="text" placeholder="输入用户名">
          </div>
          
          <div class="form-group">
            <label>密码:</label>
            <input v-model="testCredentials.password" type="password" placeholder="输入密码">
          </div>
          
          <div class="form-group">
            <label>
              <input v-model="testCredentials.rememberMe" type="checkbox">
              记住我
            </label>
          </div>
          
          <div class="button-group">
            <button @click="testLogin" :disabled="isLoading" class="btn primary">
              {{ isLoading ? '登录中...' : '测试登录' }}
            </button>
            
            <button @click="testLogout" :disabled="!isAuthenticated || isLoading" class="btn secondary">
              测试登出
            </button>
          </div>
        </div>
      </div>

      <!-- API测试 -->
      <div class="test-section">
        <h2>API测试 (自动携带Token)</h2>
        <div class="api-test">
          <div class="button-group">
            <button @click="testGetGrids" :disabled="!isAuthenticated || isLoading" class="btn primary">
              获取网格列表
            </button>
            
            <button @click="testCreateGrid" :disabled="!isAuthenticated || isLoading" class="btn success">
              创建测试网格
            </button>
            
            <button @click="testGetUserInfo" :disabled="!isAuthenticated || isLoading" class="btn info">
              获取用户信息
            </button>
            
            <button @click="testRefreshToken" :disabled="!isAuthenticated || isLoading" class="btn warning">
              手动刷新Token
            </button>
          </div>
        </div>
      </div>

      <!-- 测试结果 -->
      <div class="test-section">
        <h2>测试结果</h2>
        <div class="result-container">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            :class="['result-item', result.type]"
          >
            <div class="result-header">
              <span class="result-time">{{ result.time }}</span>
              <span class="result-title">{{ result.title }}</span>
              <span :class="['result-status', result.success ? 'success' : 'error']">
                {{ result.success ? '✅ 成功' : '❌ 失败' }}
              </span>
            </div>
            
            <div class="result-content">
              <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
        
        <button @click="clearResults" class="btn secondary small">清空结果</button>
      </div>

      <!-- 操作日志 -->
      <div class="test-section">
        <h2>操作日志</h2>
        <div class="log-container">
          <div 
            v-for="(log, index) in operationLogs" 
            :key="index"
            :class="['log-item', log.type]"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        
        <button @click="clearLogs" class="btn secondary small">清空日志</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useAuthStore } from '../stores/auth.js';
import { getGrids, createGrid } from '../services/gridApi.js';
import { getUserInfo } from '../services/authApi.js';
import http from '../utils/httpInterceptor.js';

// 认证store
const authStore = useAuthStore();

// 响应式数据
const isLoading = ref(false);
const testResults = ref([]);
const operationLogs = ref([]);

const testCredentials = reactive({
  username: 'admin',
  password: '123456',
  rememberMe: true
});

// 计算属性
const isAuthenticated = computed(() => authStore.isAuthenticated);
const currentUser = computed(() => authStore.user);
const token = computed(() => authStore.token);
const tokenExpiry = computed(() => authStore.tokenExpiry);

// 生命周期
onMounted(() => {
  addLog('info', '测试页面初始化完成');
  
  if (isAuthenticated.value) {
    addLog('success', `用户已登录: ${currentUser.value.username}`);
  } else {
    addLog('info', '用户未登录');
  }
});

// 测试登录
async function testLogin() {
  try {
    isLoading.value = true;
    addLog('info', `开始测试登录: ${testCredentials.username}`);
    
    const startTime = Date.now();
    
    await authStore.login({
      username: testCredentials.username,
      password: testCredentials.password,
      rememberMe: testCredentials.rememberMe
    });
    
    const duration = Date.now() - startTime;
    
    addResult('login', '登录测试', true, {
      user: authStore.user,
      tokenLength: authStore.token.length,
      duration: `${duration}ms`
    });
    
    addLog('success', `登录成功: ${authStore.user.username} (${duration}ms)`);
    
  } catch (error) {
    addResult('login', '登录测试', false, {
      error: error.message,
      code: error.code
    });
    
    addLog('error', `登录失败: ${error.message}`);
  } finally {
    isLoading.value = false;
  }
}

// 测试登出
async function testLogout() {
  try {
    isLoading.value = true;
    addLog('info', '开始测试登出');
    
    const startTime = Date.now();
    
    await authStore.logout();
    
    const duration = Date.now() - startTime;
    
    addResult('logout', '登出测试', true, {
      message: '登出成功',
      duration: `${duration}ms`
    });
    
    addLog('success', `登出成功 (${duration}ms)`);
    
  } catch (error) {
    addResult('logout', '登出测试', false, {
      error: error.message
    });
    
    addLog('error', `登出失败: ${error.message}`);
  } finally {
    isLoading.value = false;
  }
}

// 测试获取网格列表
async function testGetGrids() {
  try {
    isLoading.value = true;
    addLog('info', '开始测试获取网格列表API');
    
    const startTime = Date.now();
    
    const response = await getGrids({ page: 1, limit: 10 });
    
    const duration = Date.now() - startTime;
    
    addResult('api', '获取网格列表', true, {
      total: response.total,
      dataLength: response.data?.length || 0,
      duration: `${duration}ms`
    });
    
    addLog('success', `获取网格列表成功: ${response.data?.length || 0}条记录 (${duration}ms)`);
    
  } catch (error) {
    addResult('api', '获取网格列表', false, {
      error: error.message,
      code: error.code,
      status: error.status
    });
    
    addLog('error', `获取网格列表失败: ${error.message}`);
  } finally {
    isLoading.value = false;
  }
}

// 测试创建网格
async function testCreateGrid() {
  try {
    isLoading.value = true;
    addLog('info', '开始测试创建网格API');
    
    const testGridData = {
      name: `测试网格_${Date.now()}`,
      manager: '测试用户',
      status: 'active',
      description: 'API测试创建的网格',
      geometry: {
        type: 'Polygon',
        coordinates: [
          [
            [116.4070, 39.9040],
            [116.4080, 39.9040],
            [116.4080, 39.9050],
            [116.4070, 39.9050],
            [116.4070, 39.9040]
          ]
        ]
      },
      metadata: {
        createdBy: currentUser.value.id,
        createdAt: new Date().toISOString(),
        source: 'api_test',
        version: '1.0'
      },
      businessData: {
        households: 100,
        population: 300
      }
    };
    
    const startTime = Date.now();
    
    const response = await createGrid(testGridData);
    
    const duration = Date.now() - startTime;
    
    addResult('api', '创建网格', true, {
      gridId: response.data.id,
      gridName: response.data.name,
      duration: `${duration}ms`
    });
    
    addLog('success', `创建网格成功: ${response.data.name} (${duration}ms)`);
    
  } catch (error) {
    addResult('api', '创建网格', false, {
      error: error.message,
      code: error.code,
      status: error.status
    });
    
    addLog('error', `创建网格失败: ${error.message}`);
  } finally {
    isLoading.value = false;
  }
}

// 测试获取用户信息
async function testGetUserInfo() {
  try {
    isLoading.value = true;
    addLog('info', '开始测试获取用户信息API');
    
    const startTime = Date.now();
    
    const response = await getUserInfo();
    
    const duration = Date.now() - startTime;
    
    addResult('api', '获取用户信息', true, {
      user: response.data.user,
      duration: `${duration}ms`
    });
    
    addLog('success', `获取用户信息成功: ${response.data.user.username} (${duration}ms)`);
    
  } catch (error) {
    addResult('api', '获取用户信息', false, {
      error: error.message,
      code: error.code,
      status: error.status
    });
    
    addLog('error', `获取用户信息失败: ${error.message}`);
  } finally {
    isLoading.value = false;
  }
}

// 测试手动刷新Token
async function testRefreshToken() {
  try {
    isLoading.value = true;
    addLog('info', '开始测试手动刷新Token');
    
    const oldToken = authStore.token;
    const startTime = Date.now();
    
    await authStore.refreshAuthToken();
    
    const duration = Date.now() - startTime;
    const newToken = authStore.token;
    
    addResult('token', 'Token刷新', true, {
      oldTokenLength: oldToken.length,
      newTokenLength: newToken.length,
      tokenChanged: oldToken !== newToken,
      duration: `${duration}ms`
    });
    
    addLog('success', `Token刷新成功 (${duration}ms)`);
    
  } catch (error) {
    addResult('token', 'Token刷新', false, {
      error: error.message,
      code: error.code
    });
    
    addLog('error', `Token刷新失败: ${error.message}`);
  } finally {
    isLoading.value = false;
  }
}

// 添加测试结果
function addResult(type, title, success, data) {
  testResults.value.unshift({
    type,
    title,
    success,
    data,
    time: new Date().toLocaleTimeString('zh-CN')
  });
  
  // 限制结果数量
  if (testResults.value.length > 20) {
    testResults.value = testResults.value.slice(0, 20);
  }
}

// 添加操作日志
function addLog(type, message) {
  operationLogs.value.unshift({
    type,
    message,
    time: new Date().toLocaleTimeString('zh-CN')
  });
  
  // 限制日志数量
  if (operationLogs.value.length > 50) {
    operationLogs.value = operationLogs.value.slice(0, 50);
  }
}

// 清空结果
function clearResults() {
  testResults.value = [];
  addLog('info', '清空测试结果');
}

// 清空日志
function clearLogs() {
  operationLogs.value = [];
}
</script>

<style scoped>
.login-test-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

h2 {
  color: #4a90e2;
  margin-bottom: 15px;
  border-bottom: 2px solid #e1e8ed;
  padding-bottom: 8px;
}

.auth-status, .test-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.status-item label {
  font-weight: 600;
  color: #333;
  min-width: 100px;
}

.status.success {
  color: #28a745;
  font-weight: 600;
}

.status.error {
  color: #dc3545;
  font-weight: 600;
}

.token {
  font-family: monospace;
  font-size: 12px;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 4px;
}

.login-test {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  align-items: end;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group label {
  font-weight: 500;
  color: #333;
}

.form-group input[type="text"],
.form-group input[type="password"] {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.form-group input[type="checkbox"] {
  margin-right: 8px;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn.primary {
  background: #4a90e2;
  color: white;
}

.btn.secondary {
  background: #6c757d;
  color: white;
}

.btn.success {
  background: #28a745;
  color: white;
}

.btn.info {
  background: #17a2b8;
  color: white;
}

.btn.warning {
  background: #ffc107;
  color: #212529;
}

.btn.small {
  padding: 5px 10px;
  font-size: 12px;
}

.result-container, .log-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  margin-bottom: 15px;
}

.result-item, .log-item {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.result-item:last-child, .log-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.result-time, .log-time {
  font-family: monospace;
  font-size: 12px;
  color: #666;
  min-width: 80px;
}

.result-title {
  font-weight: 600;
  color: #333;
}

.result-status.success {
  color: #28a745;
  font-weight: 600;
}

.result-status.error {
  color: #dc3545;
  font-weight: 600;
}

.result-content {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 10px;
  overflow-x: auto;
}

.result-content pre {
  margin: 0;
  font-size: 12px;
  color: #333;
}

.log-item.success {
  border-left: 4px solid #28a745;
}

.log-item.error {
  border-left: 4px solid #dc3545;
}

.log-item.info {
  border-left: 4px solid #17a2b8;
}

.log-message {
  color: #333;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-test {
    grid-template-columns: 1fr;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style>

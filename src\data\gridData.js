// 共享的网格数据
export const gridData = [
  {
    id: 1,
    name: '芙蓉社区A区',
    area: 15000,
    manager: '张三',
    status: 'active',
    coordinates: [[117.197612, 31.773664], [117.199612, 31.773664], [117.199612, 31.774664], [117.197612, 31.774664]],
    centerLat: 31.774164,
    centerLng: 117.198612,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 2,
    name: '芙蓉社区B区',
    area: 12000,
    manager: '李四',
    status: 'active',
    coordinates: [[117.199612, 31.773664], [117.201612, 31.773664], [117.201612, 31.775164], [117.199612, 31.775164]],
    centerLat: 31.774414,
    centerLng: 117.200612,
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-16')
  },
  {
    id: 3,
    name: '芙蓉社区C区',
    area: 18000,
    manager: '王五',
    status: 'inactive',
    coordinates: [[116.4060, 39.9050], [116.4075, 39.9050], [116.4075, 39.9065], [116.4060, 39.9065]],
    centerLat: 39.9057,
    centerLng: 116.4067,
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-14')
  },
  {
    id: 4,
    name: '芙蓉社区D区',
    area: 14000,
    manager: '赵六',
    status: 'active',
    coordinates: [[116.4090, 39.9040], [116.4100, 39.9040], [116.4100, 39.9050], [116.4090, 39.9050]],
    centerLat: 39.9045,
    centerLng: 116.4095,
    createdAt: new Date('2024-01-14'),
    updatedAt: new Date('2024-01-18')
  },
  {
    id: 5,
    name: '芙蓉社区E区',
    area: 16000,
    manager: '孙七',
    status: 'active',
    coordinates: [[116.4050, 39.9030], [116.4070, 39.9030], [116.4070, 39.9040], [116.4050, 39.9040]],
    centerLat: 39.9035,
    centerLng: 116.4060,
    createdAt: new Date('2024-01-16'),
    updatedAt: new Date('2024-01-20')
  }
];

// 将网格数据转换为地图多边形格式
export const getGridPolygons = (selectedGridId = null) => {
  return gridData.map(grid => ({
    id: grid.id,
    title: grid.name,
    coordinates: grid.coordinates,
    color: grid.status === 'active' ? '#28a745' : '#6c757d',
    fillColor: grid.status === 'active' ? '#28a745' : '#6c757d',
    fillOpacity: selectedGridId === grid.id ? 0.4 : 0.2,
    strokeWeight: selectedGridId === grid.id ? 3 : 2,
    popup: `<div><h4>${grid.name}</h4><p>面积：${grid.area}m²</p><p>负责人：${grid.manager}</p><p>状态：${grid.status === 'active' ? '活跃' : '非活跃'}</p></div>`,
    properties: {
      gridId: grid.id,
      name: grid.name,
      area: grid.area,
      manager: grid.manager,
      status: grid.status
    }
  }));
};

// 将网格数据转换为地图标记格式
export const getGridMarkers = () => {
  return gridData.map(grid => ({
    lat: grid.centerLat,
    lng: grid.centerLng,
    title: grid.name,
    popup: `<div><h4>${grid.name}</h4><p>负责人：${grid.manager}</p><p>面积：${grid.area}m²</p><p>状态：${grid.status === 'active' ? '活跃' : '非活跃'}</p></div>`,
    properties: {
      gridId: grid.id,
      type: 'grid',
      status: grid.status
    }
  }));
};

// 获取网格统计信息
export const getGridStats = () => {
  const total = gridData.length;
  const active = gridData.filter(grid => grid.status === 'active').length;
  const inactive = total - active;
  const totalArea = gridData.reduce((sum, grid) => sum + grid.area, 0);
  
  return {
    total,
    active,
    inactive,
    totalArea
  };
};

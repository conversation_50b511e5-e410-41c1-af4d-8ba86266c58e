<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百度OCR状态检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 百度OCR状态检查</h1>
        <p>检查百度OCR API的token状态和服务可用性</p>
        
        <div class="form-group">
            <label for="tokenInput">百度OCR Access Token:</label>
            <input type="text" id="tokenInput" placeholder="输入百度OCR的access_token">
            <small>如果不输入，将使用后端配置的token</small>
        </div>

        <div class="form-group">
            <button onclick="checkTokenStatus()" id="checkBtn">🔍 检查Token状态</button>
            <button onclick="testWithBackend()" id="backendBtn">🔧 测试后端OCR</button>
            <button onclick="clearResults()">🗑️ 清除结果</button>
        </div>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        // 清除结果
        function clearResults() {
            document.getElementById('result').style.display = 'none';
        }

        // 检查Token状态
        async function checkTokenStatus() {
            const token = document.getElementById('tokenInput').value.trim();
            const checkBtn = document.getElementById('checkBtn');

            if (!token) {
                showResult('❌ 请输入百度OCR Access Token', 'error');
                return;
            }

            checkBtn.disabled = true;
            checkBtn.textContent = '🔄 检查中...';

            try {
                // 使用一个测试图片URL
                const testImageUrl = 'https://community233.oss-cn-nanjing.aliyuncs.com/images/20250624225425877.jpg';
                const apiUrl = `https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token=${token}`;
                
                console.log('🌐 测试URL:', apiUrl);
                console.log('📸 测试图片:', testImageUrl);
                
                showResult('🔄 正在检查百度OCR API状态...', 'info');

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: `id_card_side=front&url=${encodeURIComponent(testImageUrl)}`
                });
                
                const data = await response.json();
                console.log('📦 响应数据:', data);
                
                if (data.error_code) {
                    // 有错误代码
                    let resultMessage = `❌ 百度OCR API错误\n\n`;
                    resultMessage += `错误代码: ${data.error_code}\n`;
                    resultMessage += `错误信息: ${data.error_msg}\n\n`;
                    
                    if (data.error_code === 110 || data.error_msg?.includes('Access token invalid')) {
                        resultMessage += `🔍 分析: Token已失效\n`;
                        resultMessage += `💡 解决方案: 需要重新获取新的Access Token`;
                        showResult(resultMessage, 'error');
                    } else if (data.error_code === 17) {
                        resultMessage += `🔍 分析: 每日请求量超限\n`;
                        resultMessage += `💡 解决方案: 等待明天或升级API套餐`;
                        showResult(resultMessage, 'warning');
                    } else if (data.error_code === 18) {
                        resultMessage += `🔍 分析: QPS超限\n`;
                        resultMessage += `💡 解决方案: 降低请求频率`;
                        showResult(resultMessage, 'warning');
                    } else {
                        resultMessage += `🔍 分析: 其他API错误\n`;
                        resultMessage += `💡 建议: 检查图片URL或API参数`;
                        showResult(resultMessage, 'error');
                    }
                } else if (data.words_result) {
                    // 成功识别
                    let resultMessage = `✅ 百度OCR API工作正常!\n\n`;
                    resultMessage += `Token状态: 有效\n`;
                    resultMessage += `识别结果: 成功\n\n`;
                    
                    if (data.words_result.姓名) {
                        resultMessage += `识别姓名: ${data.words_result.姓名.words}\n`;
                    }
                    if (data.words_result.公民身份号码) {
                        resultMessage += `识别身份证号: ${data.words_result.公民身份号码.words}\n`;
                    }
                    
                    resultMessage += `\n完整响应:\n${JSON.stringify(data, null, 2)}`;
                    showResult(resultMessage, 'success');
                } else {
                    // 未知响应
                    let resultMessage = `🤔 未知响应格式\n\n`;
                    resultMessage += `响应内容:\n${JSON.stringify(data, null, 2)}`;
                    showResult(resultMessage, 'info');
                }

            } catch (error) {
                console.error('❌ 请求异常:', error);
                showResult(`❌ 请求异常\n\n错误信息: ${error.message}\n\n可能原因:\n1. 网络连接问题\n2. CORS限制\n3. Token格式错误`, 'error');
            } finally {
                checkBtn.disabled = false;
                checkBtn.textContent = '🔍 检查Token状态';
            }
        }

        // 测试后端OCR
        async function testWithBackend() {
            const backendBtn = document.getElementById('backendBtn');
            
            backendBtn.disabled = true;
            backendBtn.textContent = '🔄 测试中...';

            try {
                showResult('🔄 正在测试后端OCR服务...', 'info');

                // 创建一个测试用的FormData
                const formData = new FormData();
                formData.append('session_id', 'test-session-' + Date.now());
                
                // 创建一个1x1像素的测试图片
                const canvas = document.createElement('canvas');
                canvas.width = 1;
                canvas.height = 1;
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, 1, 1);
                
                canvas.toBlob(async (blob) => {
                    formData.append('image', blob, 'test.png');
                    
                    try {
                        const response = await fetch('http://localhost:8080/api/auth/ocr', {
                            method: 'POST',
                            headers: {
                                'Authorization': 'Bearer test-token'
                            },
                            body: formData
                        });

                        const result = await response.text();
                        console.log('📦 后端响应:', result);

                        let resultMessage = `📡 后端OCR测试结果\n\n`;
                        resultMessage += `状态码: ${response.status}\n`;
                        resultMessage += `响应内容:\n${result}\n\n`;

                        if (response.status === 401) {
                            resultMessage += `🔍 分析: 认证失败，需要有效的登录token`;
                            showResult(resultMessage, 'warning');
                        } else if (response.status === 500) {
                            resultMessage += `🔍 分析: 后端内部错误，可能是百度OCR配置问题`;
                            showResult(resultMessage, 'error');
                        } else if (response.ok) {
                            resultMessage += `🔍 分析: 后端服务正常运行`;
                            showResult(resultMessage, 'success');
                        } else {
                            resultMessage += `🔍 分析: 其他错误`;
                            showResult(resultMessage, 'error');
                        }

                    } catch (error) {
                        showResult(`❌ 后端测试失败\n\n错误信息: ${error.message}\n\n可能原因:\n1. 后端服务未启动\n2. 端口8080不可访问\n3. 网络连接问题`, 'error');
                    }
                }, 'image/png');

            } catch (error) {
                showResult(`❌ 测试准备失败\n\n错误信息: ${error.message}`, 'error');
            } finally {
                backendBtn.disabled = false;
                backendBtn.textContent = '🔧 测试后端OCR';
            }
        }

        // 页面加载时的提示
        window.addEventListener('load', function() {
            showResult(`💡 使用说明:

1. 输入百度OCR的Access Token来检查其状态
2. 或者直接测试后端OCR服务的可用性

常见问题:
- Token失效: 需要重新获取
- 请求量超限: 等待或升级套餐
- 后端500错误: 通常是百度OCR配置问题

建议先检查Token状态，再测试后端服务。`, 'info');
        });
    </script>
</body>
</html>

<template>
  <div class="voice-assistant">
    <!-- 语音助手按钮 -->
    <div class="voice-button" @click="toggleVoicePanel" :class="{ active: showPanel }">
      <span class="voice-icon">🎤</span>
      <span class="voice-text">语音助手</span>
    </div>

    <!-- 语音面板 -->
    <div v-if="showPanel" class="voice-panel">
      <div class="panel-header">
        <h4>语音助手</h4>
        <button @click="closePanel" class="close-btn">×</button>
      </div>

      <div class="panel-content">
        <!-- 快速TTS -->
        <div class="quick-section">
          <h5>快速朗读</h5>
          <div class="quick-buttons">
            <button @click="speakText('欢迎使用语音助手')" class="quick-btn">
              🎵 欢迎语
            </button>
            <button @click="speakText('操作完成')" class="quick-btn">
              ✅ 操作完成
            </button>
            <button @click="speakText('请注意，有新的消息')" class="quick-btn">
              📢 新消息提醒
            </button>
          </div>
        </div>

        <!-- 自定义文字转语音 -->
        <div class="custom-section">
          <h5>自定义朗读</h5>
          <div class="input-group">
            <textarea
              v-model="customText"
              placeholder="输入要朗读的文字..."
              rows="3"
              maxlength="200"
              class="text-input"
            ></textarea>
            <div class="input-actions">
              <button @click="speakCustomText" :disabled="!customText.trim()" class="action-btn primary">
                🎵 朗读
              </button>
              <button @click="stopSpeaking" class="action-btn secondary">
                ⏹️ 停止
              </button>
            </div>
          </div>
        </div>

        <!-- 语音识别 -->
        <div class="recognition-section">
          <h5>语音识别</h5>
          <div class="recognition-controls">
            <button 
              @click="toggleRecognition" 
              :disabled="!speechRecognitionSupported"
              class="recognition-btn"
              :class="{ active: isListening }"
            >
              <span v-if="isListening">🔴 停止识别</span>
              <span v-else>🎤 开始识别</span>
            </button>
          </div>
          
          <div v-if="recognizedText" class="recognition-result">
            <div class="result-text">{{ recognizedText }}</div>
            <div class="result-actions">
              <button @click="speakRecognizedText" class="result-btn">🔊 朗读</button>
              <button @click="copyToClipboard" class="result-btn">📋 复制</button>
              <button @click="clearRecognition" class="result-btn">🗑️ 清空</button>
            </div>
          </div>
        </div>

        <!-- 状态显示 -->
        <div v-if="statusMessage" class="status-message" :class="statusType">
          {{ statusMessage }}
        </div>
      </div>
    </div>

    <!-- 遮罩层 -->
    <div v-if="showPanel" class="overlay" @click="closePanel"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 响应式数据
const showPanel = ref(false)
const customText = ref('')
const recognizedText = ref('')
const isListening = ref(false)
const statusMessage = ref('')
const statusType = ref('info')

// 浏览器支持检测
const speechSynthesisSupported = ref(false)
const speechRecognitionSupported = ref(false)

// 语音对象
let speechSynthesis = null
let speechRecognition = null
let currentUtterance = null

// 初始化
const init = () => {
  speechSynthesisSupported.value = 'speechSynthesis' in window
  speechRecognitionSupported.value = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window
  
  if (speechSynthesisSupported.value) {
    speechSynthesis = window.speechSynthesis
  }
  
  if (speechRecognitionSupported.value) {
    initSpeechRecognition()
  }
}

// 初始化语音识别
const initSpeechRecognition = () => {
  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
  speechRecognition = new SpeechRecognition()
  
  speechRecognition.continuous = false
  speechRecognition.interimResults = false
  speechRecognition.lang = 'zh-CN'
  
  speechRecognition.onresult = (event) => {
    const result = event.results[0][0].transcript
    recognizedText.value = result
    showStatus('识别成功', 'success')
  }
  
  speechRecognition.onstart = () => {
    isListening.value = true
    showStatus('正在监听...', 'info')
  }
  
  speechRecognition.onend = () => {
    isListening.value = false
  }
  
  speechRecognition.onerror = (event) => {
    isListening.value = false
    let errorMsg = '识别失败'
    switch (event.error) {
      case 'no-speech':
        errorMsg = '未检测到语音'
        break
      case 'not-allowed':
        errorMsg = '麦克风权限被拒绝'
        break
      default:
        errorMsg = '识别出错'
    }
    showStatus(errorMsg, 'error')
  }
}

// 面板控制
const toggleVoicePanel = () => {
  showPanel.value = !showPanel.value
}

const closePanel = () => {
  showPanel.value = false
  stopSpeaking()
  if (isListening.value) {
    stopRecognition()
  }
}

// TTS 功能
const speakText = (text) => {
  if (!speechSynthesisSupported.value || !text) return
  
  speechSynthesis.cancel()
  
  currentUtterance = new SpeechSynthesisUtterance(text)
  currentUtterance.rate = 1
  currentUtterance.pitch = 1
  currentUtterance.volume = 1
  
  currentUtterance.onstart = () => {
    showStatus('正在播放...', 'info')
  }
  
  currentUtterance.onend = () => {
    showStatus('播放完成', 'success')
  }
  
  currentUtterance.onerror = () => {
    showStatus('播放失败', 'error')
  }
  
  speechSynthesis.speak(currentUtterance)
}

const speakCustomText = () => {
  speakText(customText.value)
}

const stopSpeaking = () => {
  if (speechSynthesis) {
    speechSynthesis.cancel()
    showStatus('已停止播放', 'info')
  }
}

// STT 功能
const toggleRecognition = () => {
  if (isListening.value) {
    stopRecognition()
  } else {
    startRecognition()
  }
}

const startRecognition = () => {
  if (!speechRecognitionSupported.value) {
    showStatus('浏览器不支持语音识别', 'error')
    return
  }
  
  try {
    speechRecognition.start()
  } catch (error) {
    showStatus('启动识别失败', 'error')
  }
}

const stopRecognition = () => {
  if (speechRecognition && isListening.value) {
    speechRecognition.stop()
  }
}

const speakRecognizedText = () => {
  speakText(recognizedText.value)
}

const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(recognizedText.value)
    showStatus('已复制到剪贴板', 'success')
  } catch (error) {
    showStatus('复制失败', 'error')
  }
}

const clearRecognition = () => {
  recognizedText.value = ''
  showStatus('已清空', 'info')
}

// 状态显示
const showStatus = (message, type = 'info') => {
  statusMessage.value = message
  statusType.value = type
  
  setTimeout(() => {
    statusMessage.value = ''
  }, 3000)
}

// 生命周期
onMounted(() => {
  init()
})

onUnmounted(() => {
  if (speechSynthesis) {
    speechSynthesis.cancel()
  }
  if (speechRecognition && isListening.value) {
    speechRecognition.stop()
  }
})

// 暴露方法给父组件
defineExpose({
  speak: speakText,
  stop: stopSpeaking,
  show: () => { showPanel.value = true },
  hide: () => { showPanel.value = false }
})
</script>

<style scoped>
.voice-assistant {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.voice-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #20B2AA;
  color: white;
  border-radius: 25px;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(32, 178, 170, 0.3);
  transition: all 0.3s ease;
  user-select: none;
}

.voice-button:hover {
  background: #1a9a93;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(32, 178, 170, 0.4);
}

.voice-button.active {
  background: #1a9a93;
}

.voice-icon {
  font-size: 18px;
}

.voice-text {
  font-weight: 500;
  font-size: 14px;
}

.voice-panel {
  position: absolute;
  bottom: 60px;
  right: 0;
  width: 350px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid #e1e5e9;
  max-height: 500px;
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.panel-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.panel-content {
  padding: 20px;
}

.quick-section, .custom-section, .recognition-section {
  margin-bottom: 20px;
}

.quick-section h5, .custom-section h5, .recognition-section h5 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.quick-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-btn {
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  font-size: 13px;
}

.quick-btn:hover {
  background: #e9ecef;
  border-color: #20B2AA;
}

.text-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  resize: vertical;
  font-size: 14px;
  box-sizing: border-box;
}

.text-input:focus {
  outline: none;
  border-color: #20B2AA;
}

.input-actions {
  display: flex;
  gap: 8px;
  margin-top: 10px;
}

.action-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: #20B2AA;
  color: white;
}

.action-btn.primary:hover:not(:disabled) {
  background: #1a9a93;
}

.action-btn.secondary {
  background: #6c757d;
  color: white;
}

.action-btn.secondary:hover {
  background: #5a6268;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.recognition-btn {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.recognition-btn.active {
  border-color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.recognition-btn:not(.active):hover:not(:disabled) {
  border-color: #20B2AA;
  background: rgba(32, 178, 170, 0.1);
}

.recognition-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.recognition-result {
  margin-top: 15px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.result-text {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.4;
}

.result-actions {
  display: flex;
  gap: 6px;
}

.result-btn {
  padding: 6px 10px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.result-btn:hover {
  background: #e9ecef;
  border-color: #20B2AA;
}

.status-message {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  text-align: center;
  margin-top: 15px;
}

.status-message.info {
  background: rgba(32, 178, 170, 0.1);
  color: #20B2AA;
  border: 1px solid rgba(32, 178, 170, 0.3);
}

.status-message.success {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-message.error {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: -1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .voice-assistant {
    bottom: 15px;
    right: 15px;
  }
  
  .voice-panel {
    width: 300px;
    max-height: 400px;
  }
  
  .voice-button {
    padding: 10px 16px;
  }
  
  .voice-text {
    display: none;
  }
}
</style>

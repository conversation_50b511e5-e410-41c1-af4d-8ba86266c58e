<template>
  <div class="vote-submit-test">
    <div class="test-header">
      <h1>投票提交功能测试</h1>
      <p>测试真实的投票提交API</p>
    </div>

    <div class="test-content">
      <!-- 测试配置 -->
      <div class="test-config">
        <h3>测试配置</h3>
        <div class="config-item">
          <label>投票ID:</label>
          <input v-model="testVoteId" type="number" placeholder="输入投票ID">
        </div>
        <div class="config-item">
          <label>选项ID (多个用逗号分隔):</label>
          <input v-model="testOptionIds" type="text" placeholder="例如: 1,2">
        </div>
        <div class="config-item">
          <label>Token:</label>
          <input v-model="testToken" type="text" placeholder="JWT Token">
          <button @click="loadTokenFromStorage" class="load-token-btn">从存储加载</button>
        </div>
      </div>

      <!-- 测试按钮 -->
      <div class="test-actions">
        <button @click="testVoteSubmit" class="test-btn primary" :disabled="isSubmitting">
          {{ isSubmitting ? '提交中...' : '测试投票提交' }}
        </button>
        <button @click="testGetVoteList" class="test-btn">测试获取投票列表</button>
        <button @click="clearResults" class="test-btn secondary">清除结果</button>
      </div>

      <!-- 测试结果 -->
      <div class="test-results">
        <h3>测试结果</h3>
        <div class="result-container">
          <div
            v-for="(result, index) in testResults"
            :key="index"
            :class="['result-item', result.type]"
          >
            <div class="result-header">
              <span class="result-time">{{ result.time }}</span>
              <span class="result-type">{{ result.title }}</span>
            </div>
            <div class="result-content">
              <pre>{{ result.content }}</pre>
            </div>
          </div>
        </div>
      </div>

      <!-- API文档 -->
      <div class="api-docs">
        <h3>API文档</h3>
        <div class="api-item">
          <h4>投票提交 API</h4>
          <p><strong>URL:</strong> POST /api/vote/{voteId}</p>
          <p><strong>Headers:</strong> Authorization: Bearer {token}</p>
          <p><strong>Body:</strong></p>
          <pre>[1, 2]</pre>
          <p><strong>说明:</strong> 直接发送选项ID数组，不需要包装在对象中</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { getToken } from '../utils/tokenManager.js';
import { submitVote, getVoteList } from '../services/voteApi.js';

// 测试配置
const testVoteId = ref(1);
const testOptionIds = ref('1');
const testToken = ref('');
const isSubmitting = ref(false);

// 测试结果
const testResults = ref([]);

// 添加测试结果
const addResult = (title, content, type = 'info') => {
  testResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    title,
    content: typeof content === 'object' ? JSON.stringify(content, null, 2) : content,
    type
  });
};

// 清除结果
const clearResults = () => {
  testResults.value = [];
};

// 从存储加载Token
const loadTokenFromStorage = () => {
  const token = getToken();
  if (token) {
    testToken.value = token;
    addResult('Token加载', `已从存储加载Token: ${token.substring(0, 20)}...`, 'success');
  } else {
    addResult('Token加载', '存储中未找到Token', 'error');
  }
};

// 测试投票提交
const testVoteSubmit = async () => {
  if (isSubmitting.value) return;

  const voteId = parseInt(testVoteId.value);
  const optionIds = testOptionIds.value.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));

  if (!voteId || optionIds.length === 0) {
    addResult('参数验证', '请输入有效的投票ID和选项ID', 'error');
    return;
  }

  isSubmitting.value = true;
  addResult('开始测试', `投票ID: ${voteId}, 选项ID: [${optionIds.join(', ')}]`, 'info');

  try {
    // 如果有测试Token，临时设置到localStorage
    if (testToken.value) {
      const originalToken = getToken();
      localStorage.setItem('auth_token', testToken.value);
      
      try {
        const result = await submitVote(voteId, optionIds);
        addResult('投票提交成功', result, 'success');
      } finally {
        // 恢复原始Token
        if (originalToken) {
          localStorage.setItem('auth_token', originalToken);
        } else {
          localStorage.removeItem('auth_token');
        }
      }
    } else {
      const result = await submitVote(voteId, optionIds);
      addResult('投票提交成功', result, 'success');
    }

  } catch (error) {
    addResult('投票提交失败', {
      message: error.message,
      stack: error.stack
    }, 'error');
  } finally {
    isSubmitting.value = false;
  }
};

// 测试获取投票列表
const testGetVoteList = async () => {
  addResult('开始测试', '获取投票列表', 'info');

  try {
    const result = await getVoteList();
    addResult('获取投票列表成功', result, 'success');
  } catch (error) {
    addResult('获取投票列表失败', {
      message: error.message,
      stack: error.stack
    }, 'error');
  }
};

// 页面加载时自动加载Token
loadTokenFromStorage();
</script>

<style scoped>
.vote-submit-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.test-content {
  display: grid;
  gap: 20px;
}

.test-config {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 10px;
}

.config-item label {
  min-width: 150px;
  font-weight: 500;
}

.config-item input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.load-token-btn {
  padding: 8px 16px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.test-btn.primary {
  background: #007bff;
  color: white;
}

.test-btn.primary:hover:not(:disabled) {
  background: #0056b3;
}

.test-btn.primary:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.test-btn.secondary {
  background: #6c757d;
  color: white;
}

.test-btn.secondary:hover {
  background: #5a6268;
}

.test-btn:not(.primary):not(.secondary) {
  background: #28a745;
  color: white;
}

.test-btn:not(.primary):not(.secondary):hover {
  background: #218838;
}

.test-results {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.result-container {
  max-height: 500px;
  overflow-y: auto;
}

.result-item {
  margin-bottom: 15px;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid;
}

.result-item.success {
  background: #d4edda;
  border-left-color: #28a745;
}

.result-item.error {
  background: #f8d7da;
  border-left-color: #dc3545;
}

.result-item.info {
  background: #d1ecf1;
  border-left-color: #17a2b8;
}

.result-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-weight: 600;
}

.result-content pre {
  background: rgba(0, 0, 0, 0.05);
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  margin: 0;
}

.api-docs {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.api-item {
  margin-bottom: 20px;
}

.api-item h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.api-item p {
  margin: 5px 0;
}

.api-item pre {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  font-size: 14px;
  overflow-x: auto;
}
</style>

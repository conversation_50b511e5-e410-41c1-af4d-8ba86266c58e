/**
 * 投票API测试脚本
 * 可以在浏览器控制台中运行，用于快速测试投票API
 */

// 测试配置
const TEST_CONFIG = {
  baseUrl: 'http://localhost:8080/api',
  voteId: 1,
  optionIds: [1],
  token: '' // 在这里填入你的JWT token
};

/**
 * 从localStorage获取token
 */
function getTokenFromStorage() {
  const token = localStorage.getItem('auth_token');
  if (token) {
    TEST_CONFIG.token = token;
    console.log('✅ 已从localStorage获取token:', token.substring(0, 20) + '...');
    return token;
  } else {
    console.log('❌ localStorage中未找到token');
    return null;
  }
}

/**
 * 测试投票提交API
 */
async function testVoteSubmit(voteId = TEST_CONFIG.voteId, optionIds = TEST_CONFIG.optionIds, token = TEST_CONFIG.token) {
  console.log('🔧 开始测试投票提交API');
  console.log('📊 测试参数:', { voteId, optionIds, token: token ? token.substring(0, 20) + '...' : 'null' });

  if (!token) {
    console.log('⚠️ 未提供token，尝试从localStorage获取...');
    token = getTokenFromStorage();
    if (!token) {
      console.error('❌ 无法获取token，请先登录或手动设置token');
      return;
    }
  }

  const url = `${TEST_CONFIG.baseUrl}/vote/${voteId}`;
  // 根据后端API实际格式，直接发送选项ID数组
  const requestBody = optionIds;

  console.log('📡 请求URL:', url);
  console.log('📦 请求体 (选项ID数组):', JSON.stringify(requestBody, null, 2));

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(requestBody)
    });

    console.log('📈 响应状态:', response.status, response.statusText);

    const responseData = await response.json();
    console.log('📋 响应数据:', JSON.stringify(responseData, null, 2));

    if (response.ok) {
      console.log('✅ 投票提交成功!');
      return { success: true, data: responseData };
    } else {
      console.log('❌ 投票提交失败:', responseData.msg || '未知错误');
      return { success: false, error: responseData };
    }

  } catch (error) {
    console.error('💥 请求异常:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 测试获取投票列表API
 */
async function testGetVoteList(token = TEST_CONFIG.token) {
  console.log('🔧 开始测试获取投票列表API');

  if (!token) {
    token = getTokenFromStorage();
    if (!token) {
      console.error('❌ 无法获取token，请先登录或手动设置token');
      return;
    }
  }

  const url = `${TEST_CONFIG.baseUrl}/vote/list`;
  console.log('📡 请求URL:', url);

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('📈 响应状态:', response.status, response.statusText);

    const responseData = await response.json();
    console.log('📋 响应数据:', JSON.stringify(responseData, null, 2));

    if (response.ok) {
      console.log('✅ 获取投票列表成功!');
      console.log('📊 投票数量:', responseData.data?.length || 0);
      
      // 显示投票选项信息
      if (responseData.data && responseData.data.length > 0) {
        responseData.data.forEach((vote, index) => {
          console.log(`📝 投票${index + 1}: ${vote.title}`);
          if (vote.options) {
            vote.options.forEach(option => {
              console.log(`  - 选项ID: ${option.id || option.sortOrder}, 内容: ${option.content}`);
            });
          }
        });
      }
      
      return { success: true, data: responseData };
    } else {
      console.log('❌ 获取投票列表失败:', responseData.msg || '未知错误');
      return { success: false, error: responseData };
    }

  } catch (error) {
    console.error('💥 请求异常:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 运行完整测试
 */
async function runFullTest() {
  console.log('🚀 开始运行完整的投票API测试');
  console.log('=' .repeat(50));

  // 1. 获取token
  const token = getTokenFromStorage();
  if (!token) {
    console.error('❌ 测试终止：无法获取有效token');
    return;
  }

  // 2. 测试获取投票列表
  console.log('\n📋 测试1: 获取投票列表');
  console.log('-'.repeat(30));
  const listResult = await testGetVoteList(token);
  
  if (!listResult.success) {
    console.error('❌ 获取投票列表失败，跳过投票提交测试');
    return;
  }

  // 3. 测试投票提交
  console.log('\n🗳️ 测试2: 投票提交');
  console.log('-'.repeat(30));
  
  // 如果有投票数据，使用第一个投票进行测试
  if (listResult.data?.data && listResult.data.data.length > 0) {
    const firstVote = listResult.data.data[0];
    const voteId = firstVote.id;
    const firstOptionId = firstVote.options?.[0]?.id || firstVote.options?.[0]?.sortOrder;
    
    if (voteId && firstOptionId) {
      console.log(`📊 使用投票ID: ${voteId}, 选项ID: ${firstOptionId}`);
      await testVoteSubmit(voteId, [firstOptionId], token);
    } else {
      console.log('⚠️ 投票数据不完整，使用默认测试参数');
      await testVoteSubmit(TEST_CONFIG.voteId, TEST_CONFIG.optionIds, token);
    }
  } else {
    console.log('⚠️ 没有可用的投票数据，使用默认测试参数');
    await testVoteSubmit(TEST_CONFIG.voteId, TEST_CONFIG.optionIds, token);
  }

  console.log('\n🎉 测试完成!');
  console.log('=' .repeat(50));
}

// 导出函数供控制台使用
window.voteApiTest = {
  testVoteSubmit,
  testGetVoteList,
  runFullTest,
  getTokenFromStorage,
  config: TEST_CONFIG
};

console.log('🔧 投票API测试工具已加载!');
console.log('📖 使用方法:');
console.log('  - voteApiTest.runFullTest() - 运行完整测试');
console.log('  - voteApiTest.testGetVoteList() - 测试获取投票列表');
console.log('  - voteApiTest.testVoteSubmit(voteId, [optionId1, optionId2]) - 测试投票提交');
console.log('  - voteApiTest.getTokenFromStorage() - 获取当前token');
console.log('  - voteApiTest.config - 查看/修改测试配置');

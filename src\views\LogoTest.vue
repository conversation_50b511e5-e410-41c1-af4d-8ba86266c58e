<template>
  <div class="logo-test">
    <h1>Logo测试页面</h1>
    
    <div class="logo-showcase">
      <h2>不同尺寸的Logo展示</h2>
      
      <div class="logo-row">
        <div class="logo-item">
          <h3>小尺寸 (24px)</h3>
          <GongdaLogo :size="24" />
        </div>
        
        <div class="logo-item">
          <h3>中等尺寸 (40px)</h3>
          <GongdaLogo :size="40" />
        </div>
        
        <div class="logo-item">
          <h3>大尺寸 (64px)</h3>
          <GongdaLogo :size="64" />
        </div>
        
        <div class="logo-item">
          <h3>超大尺寸 (120px)</h3>
          <GongdaLogo :size="120" />
        </div>
      </div>
      
      <div class="logo-backgrounds">
        <h2>不同背景下的Logo展示</h2>
        
        <div class="bg-white">
          <h3>白色背景</h3>
          <GongdaLogo :size="60" />
        </div>
        
        <div class="bg-dark">
          <h3>深色背景</h3>
          <GongdaLogo :size="60" />
        </div>
        
        <div class="bg-blue">
          <h3>蓝色背景</h3>
          <GongdaLogo :size="60" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import GongdaLogo from '../components/GongdaLogo.vue';
</script>

<style scoped>
.logo-test {
  padding: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 40px;
}

h2 {
  color: #555;
  margin: 30px 0 20px;
  border-bottom: 2px solid #ddd;
  padding-bottom: 10px;
}

.logo-row {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 40px;
}

.logo-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
}

.logo-item h3 {
  margin-bottom: 15px;
  color: #666;
  font-size: 14px;
}

.logo-backgrounds {
  margin-top: 40px;
}

.logo-backgrounds > div {
  display: inline-block;
  margin: 20px;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
  min-width: 200px;
}

.bg-white {
  background: white;
  border: 1px solid #ddd;
}

.bg-dark {
  background: #333;
  color: white;
}

.bg-blue {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
}

.logo-backgrounds h3 {
  margin-bottom: 20px;
  font-size: 16px;
}
</style>

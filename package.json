{"name": "furong-community-management", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000 --host 127.0.0.1", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@turf/turf": "^7.2.0", "axios": "^1.10.0", "chart.js": "^4.5.0", "leaflet": "^1.9.4", "pinia": "^3.0.3", "vue": "^3.4.21", "vue-chartjs": "^5.3.2", "vue-leaflet": "^0.1.0", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "vite": "^5.2.0"}}
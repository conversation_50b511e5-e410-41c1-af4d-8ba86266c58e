/**
 * 对话框插件
 * 将对话框服务注册为全局属性，方便在组件中使用
 */

import dialog from '../utils/dialog.js'

export default {
  install(app) {
    // 将对话框服务添加到全局属性
    app.config.globalProperties.$dialog = dialog
    app.config.globalProperties.$alert = dialog.alert
    app.config.globalProperties.$confirm = dialog.confirm
    app.config.globalProperties.$prompt = dialog.prompt
    app.config.globalProperties.$showInfo = dialog.showInfo
    app.config.globalProperties.$showSuccess = dialog.showSuccess
    app.config.globalProperties.$showWarning = dialog.showWarning
    app.config.globalProperties.$showError = dialog.showError
    app.config.globalProperties.$showConfirm = dialog.showConfirm
    app.config.globalProperties.$showPrompt = dialog.showPrompt
    app.config.globalProperties.$showTextareaPrompt = dialog.showTextareaPrompt
    app.config.globalProperties.$showLoading = dialog.showLoading
    app.config.globalProperties.$closeDialog = dialog.closeDialog
    app.config.globalProperties.$updateLoading = dialog.updateLoading

    // 提供组合式API的注入
    app.provide('dialog', dialog)
  }
}

<template>
  <div class="dialog-demo">
    <div class="page-header">
      <h1 class="page-title">
        <span class="title-icon">💬</span>
        自定义弹窗演示
      </h1>
      <p class="page-description">展示各种类型的自定义弹窗效果</p>
    </div>

    <div class="demo-content">
      <div class="demo-section">
        <h2>基础弹窗</h2>
        <div class="demo-buttons">
          <button class="demo-btn info" @click="showInfoDemo">
            ℹ️ 信息提示
          </button>
          <button class="demo-btn success" @click="showSuccessDemo">
            ✅ 成功提示
          </button>
          <button class="demo-btn warning" @click="showWarningDemo">
            ⚠️ 警告提示
          </button>
          <button class="demo-btn error" @click="showErrorDemo">
            ❌ 错误提示
          </button>
        </div>
      </div>

      <div class="demo-section">
        <h2>交互弹窗</h2>
        <div class="demo-buttons">
          <button class="demo-btn confirm" @click="showConfirmDemo">
            ❓ 确认对话框
          </button>
          <button class="demo-btn prompt" @click="showPromptDemo">
            📝 输入对话框
          </button>
          <button class="demo-btn textarea" @click="showTextareaDemo">
            📄 文本域输入
          </button>
          <button class="demo-btn loading" @click="showLoadingDemo">
            ⏳ 加载对话框
          </button>
        </div>
      </div>

      <div class="demo-section">
        <h2>兼容性方法</h2>
        <div class="demo-buttons">
          <button class="demo-btn compat" @click="showAlertDemo">
            🔔 Alert 替代
          </button>
          <button class="demo-btn compat" @click="showConfirmCompatDemo">
            🤔 Confirm 替代
          </button>
          <button class="demo-btn compat" @click="showPromptCompatDemo">
            ✏️ Prompt 替代
          </button>
        </div>
      </div>

      <div class="demo-section">
        <h2>结果显示</h2>
        <div class="result-display">
          <h3>最后操作结果：</h3>
          <pre>{{ lastResult }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useDialog } from '../composables/useDialog.js'

const dialog = useDialog()
const lastResult = ref('暂无操作')

// 基础弹窗演示
const showInfoDemo = async () => {
  try {
    const result = await dialog.showInfo('这是一个信息提示弹窗', '信息')
    lastResult.value = `信息提示结果: ${JSON.stringify(result, null, 2)}`
  } catch (error) {
    lastResult.value = `操作取消: ${error.message}`
  }
}

const showSuccessDemo = async () => {
  try {
    const result = await dialog.showSuccess('操作成功完成！', '成功')
    lastResult.value = `成功提示结果: ${JSON.stringify(result, null, 2)}`
  } catch (error) {
    lastResult.value = `操作取消: ${error.message}`
  }
}

const showWarningDemo = async () => {
  try {
    const result = await dialog.showWarning('请注意这个警告信息', '警告')
    lastResult.value = `警告提示结果: ${JSON.stringify(result, null, 2)}`
  } catch (error) {
    lastResult.value = `操作取消: ${error.message}`
  }
}

const showErrorDemo = async () => {
  try {
    const result = await dialog.showError('发生了一个错误！', '错误')
    lastResult.value = `错误提示结果: ${JSON.stringify(result, null, 2)}`
  } catch (error) {
    lastResult.value = `操作取消: ${error.message}`
  }
}

// 交互弹窗演示
const showConfirmDemo = async () => {
  try {
    const result = await dialog.showConfirm('确定要执行这个操作吗？', '确认操作')
    lastResult.value = `确认对话框结果: ${JSON.stringify(result, null, 2)}`
  } catch (error) {
    lastResult.value = `操作取消: ${JSON.stringify(error, null, 2)}`
  }
}

const showPromptDemo = async () => {
  try {
    const result = await dialog.showPrompt('请输入您的姓名：', '输入信息', {
      inputPlaceholder: '请输入姓名...'
    })
    lastResult.value = `输入对话框结果: ${JSON.stringify(result, null, 2)}`
  } catch (error) {
    lastResult.value = `操作取消: ${JSON.stringify(error, null, 2)}`
  }
}

const showTextareaDemo = async () => {
  try {
    const result = await dialog.showTextareaPrompt('请输入您的意见或建议：', '意见反馈', {
      inputPlaceholder: '请详细描述您的意见...'
    })
    lastResult.value = `文本域输入结果: ${JSON.stringify(result, null, 2)}`
  } catch (error) {
    lastResult.value = `操作取消: ${JSON.stringify(error, null, 2)}`
  }
}

const showLoadingDemo = async () => {
  try {
    // 显示加载对话框
    const loadingPromise = dialog.showLoading('正在处理，请稍候...', '处理中')
    
    // 模拟异步操作
    setTimeout(() => {
      dialog.updateLoading(false)
      dialog.closeDialog()
      lastResult.value = '加载完成！'
    }, 3000)
    
    await loadingPromise
  } catch (error) {
    lastResult.value = `加载取消: ${error.message}`
  }
}

// 兼容性方法演示
const showAlertDemo = async () => {
  try {
    const result = await dialog.alert('这是一个 Alert 替代弹窗')
    lastResult.value = `Alert 替代结果: ${JSON.stringify(result, null, 2)}`
  } catch (error) {
    lastResult.value = `操作取消: ${error.message}`
  }
}

const showConfirmCompatDemo = async () => {
  try {
    const confirmed = await dialog.confirm('确定要删除这个项目吗？')
    lastResult.value = `Confirm 替代结果: ${confirmed}`
  } catch (error) {
    lastResult.value = `操作取消: ${error.message}`
  }
}

const showPromptCompatDemo = async () => {
  try {
    const input = await dialog.prompt('请输入新的名称：', '默认名称')
    lastResult.value = `Prompt 替代结果: ${input}`
  } catch (error) {
    lastResult.value = `操作取消: ${error.message}`
  }
}
</script>

<style scoped>
.dialog-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.title-icon {
  font-size: 2rem;
}

.page-description {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin: 0;
}

.demo-content {
  max-width: 1000px;
  margin: 0 auto;
}

.demo-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-section h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.demo-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.demo-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.demo-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.demo-btn.info {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.demo-btn.success {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.demo-btn.warning {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.demo-btn.error {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.demo-btn.confirm {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.demo-btn.prompt {
  background: linear-gradient(135deg, #1abc9c, #16a085);
}

.demo-btn.textarea {
  background: linear-gradient(135deg, #34495e, #2c3e50);
}

.demo-btn.loading {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

.demo-btn.compat {
  background: linear-gradient(135deg, #e67e22, #d35400);
}

.result-display {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #3498db;
}

.result-display h3 {
  margin-top: 0;
  color: #2c3e50;
}

.result-display pre {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  margin: 0;
}
</style>

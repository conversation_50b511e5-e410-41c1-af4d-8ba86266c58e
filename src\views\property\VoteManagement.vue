<template>
  <div class="vote-management">
    <div class="page-header">
      <h1>投票管理</h1>
      <p class="subtitle">创建和管理社区投票活动</p>
    </div>

    <div class="management-content">
      <!-- 创建投票表单 -->
      <div class="create-vote-section">
        <div class="section-header">
          <h2>创建新投票</h2>
          <button 
            v-if="!showCreateForm" 
            @click="showCreateForm = true" 
            class="btn primary"
          >
            <span class="btn-icon">➕</span>
            创建投票
          </button>
        </div>

        <div v-if="showCreateForm" class="create-form-container">
          <form @submit.prevent="submitVote" class="vote-form">
            <!-- 基本信息 -->
            <div class="form-section">
              <h3>基本信息</h3>
              
              <div class="form-group">
                <label for="title">投票标题 *</label>
                <input
                  id="title"
                  v-model="voteForm.title"
                  type="text"
                  placeholder="请输入投票标题"
                  maxlength="100"
                  required
                />
                <div class="char-count">{{ voteForm.title.length }}/100</div>
              </div>

              <div class="form-group">
                <label for="description">投票描述 *</label>
                <textarea
                  id="description"
                  v-model="voteForm.description"
                  placeholder="请输入投票描述"
                  maxlength="500"
                  rows="4"
                  required
                ></textarea>
                <div class="char-count">{{ voteForm.description.length }}/500</div>
              </div>
            </div>

            <!-- 时间设置 -->
            <div class="form-section">
              <h3>时间设置</h3>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="startTime">开始时间 *</label>
                  <input
                    id="startTime"
                    v-model="voteForm.startTime"
                    type="datetime-local"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="endTime">结束时间 *</label>
                  <input
                    id="endTime"
                    v-model="voteForm.endTime"
                    type="datetime-local"
                    required
                  />
                </div>
              </div>
            </div>

            <!-- 投票设置 -->
            <div class="form-section">
              <h3>投票设置</h3>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="maxChoices">最大选择数 *</label>
                  <input
                    id="maxChoices"
                    v-model.number="voteForm.maxChoices"
                    type="number"
                    min="1"
                    required
                  />
                </div>

                <div class="form-group">
                  <label class="checkbox-label">
                    <input
                      v-model="voteForm.isAnonymous"
                      type="checkbox"
                    />
                    <span class="checkbox-text">匿名投票</span>
                  </label>
                </div>
              </div>
            </div>

            <!-- 投票选项 -->
            <div class="form-section">
              <h3>投票选项</h3>
              
              <div class="options-container">
                <div
                  v-for="(option, index) in voteForm.options"
                  :key="index"
                  class="option-item"
                >
                  <div class="option-number">{{ index + 1 }}</div>
                  <input
                    v-model="option.content"
                    type="text"
                    placeholder="请输入选项内容"
                    maxlength="100"
                    required
                  />
                  <button
                    v-if="voteForm.options.length > 2"
                    @click="removeOption(index)"
                    type="button"
                    class="btn-remove"
                  >
                    ✕
                  </button>
                </div>
              </div>

              <button
                @click="addOption"
                type="button"
                class="btn secondary"
              >
                <span class="btn-icon">➕</span>
                添加选项
              </button>
            </div>

            <!-- 投票范围 -->
            <div class="form-section">
              <h3>投票范围</h3>
              
              <div class="scopes-container">
                <div
                  v-for="(scope, index) in voteForm.scopes"
                  :key="index"
                  class="scope-item"
                >
                  <select v-model="scope.targetType" required>
                    <option value="">请选择范围类型</option>
                    <option value="1">社区</option>
                    <option value="2">网格</option>
                    <option value="3">楼栋</option>
                  </select>
                  
                  <input
                    v-model.number="scope.targetId"
                    type="number"
                    placeholder="请输入ID"
                    min="1"
                    required
                  />
                  
                  <button
                    v-if="voteForm.scopes.length > 1"
                    @click="removeScope(index)"
                    type="button"
                    class="btn-remove"
                  >
                    ✕
                  </button>
                </div>
              </div>

              <button
                @click="addScope"
                type="button"
                class="btn secondary"
              >
                <span class="btn-icon">➕</span>
                添加范围
              </button>
            </div>

            <!-- 表单操作 -->
            <div class="form-actions">
              <button
                @click="cancelCreate"
                type="button"
                class="btn secondary"
              >
                取消
              </button>
              <button
                type="submit"
                class="btn primary"
                :disabled="isSubmitting"
              >
                <span v-if="isSubmitting">创建中...</span>
                <span v-else>创建投票</span>
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- 错误提示 -->
      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <!-- 成功提示 -->
      <div v-if="successMessage" class="success-message">
        {{ successMessage }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { createVote, validateVoteData } from '../../services/voteApi.js';

// 响应式数据
const showCreateForm = ref(false);
const isSubmitting = ref(false);
const errorMessage = ref('');
const successMessage = ref('');

// 投票表单数据
const voteForm = reactive({
  title: '',
  description: '',
  startTime: '',
  endTime: '',
  maxChoices: 1,
  isAnonymous: false,
  options: [
    { sortOrder: 1, content: '' },
    { sortOrder: 2, content: '' }
  ],
  scopes: [
    { targetType: '', targetId: null }
  ]
});

// 添加选项
const addOption = () => {
  voteForm.options.push({
    sortOrder: voteForm.options.length + 1,
    content: ''
  });
};

// 删除选项
const removeOption = (index) => {
  if (voteForm.options.length > 2) {
    voteForm.options.splice(index, 1);
    // 重新排序
    voteForm.options.forEach((option, idx) => {
      option.sortOrder = idx + 1;
    });
  }
};

// 添加投票范围
const addScope = () => {
  voteForm.scopes.push({
    targetType: '',
    targetId: null
  });
};

// 删除投票范围
const removeScope = (index) => {
  if (voteForm.scopes.length > 1) {
    voteForm.scopes.splice(index, 1);
  }
};

// 取消创建
const cancelCreate = () => {
  showCreateForm.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  voteForm.title = '';
  voteForm.description = '';
  voteForm.startTime = '';
  voteForm.endTime = '';
  voteForm.maxChoices = 1;
  voteForm.isAnonymous = false;
  voteForm.options = [
    { sortOrder: 1, content: '' },
    { sortOrder: 2, content: '' }
  ];
  voteForm.scopes = [
    { targetType: '', targetId: null }
  ];
  errorMessage.value = '';
  successMessage.value = '';
};

// 提交投票
const submitVote = async () => {
  if (isSubmitting.value) return;

  errorMessage.value = '';
  successMessage.value = '';

  // 准备提交数据
  const submitData = {
    title: voteForm.title.trim(),
    description: voteForm.description.trim(),
    startTime: voteForm.startTime.replace('T', ' ') + ':00',
    endTime: voteForm.endTime.replace('T', ' ') + ':00',
    maxChoices: voteForm.maxChoices,
    isAnonymous: voteForm.isAnonymous ? 1 : 0,
    options: voteForm.options.map(option => ({
      sortOrder: option.sortOrder,
      content: option.content.trim()
    })),
    scopes: voteForm.scopes.map(scope => ({
      targetType: parseInt(scope.targetType),
      targetId: scope.targetId
    }))
  };

  // 验证数据
  const validation = validateVoteData(submitData);
  if (!validation.isValid) {
    errorMessage.value = validation.errors.join('; ');
    return;
  }

  isSubmitting.value = true;

  try {
    console.log('🔧 提交投票数据:', submitData);

    const result = await createVote(submitData);

    if (result.success) {
      successMessage.value = '投票创建成功！';
      showCreateForm.value = false;
      resetForm();
      console.log('✅ 投票创建成功');
    } else {
      throw new Error(result.message || '投票创建失败');
    }

  } catch (error) {
    console.error('❌ 投票创建失败:', error);
    errorMessage.value = error.message || '投票创建失败，请稍后重试';
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped>
.vote-management {
  background: #f5f7fa;
  min-height: 100vh;
  padding: 20px;
}

.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
}

.management-content {
  max-width: 1200px;
  margin: 0 auto;
}

.create-vote-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

.section-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.create-form-container {
  padding: 30px;
}

.vote-form {
  max-width: 800px;
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h3 {
  margin: 0 0 20px 0;
  color: #34495e;
  font-size: 18px;
  font-weight: 600;
}

.form-group {
  margin-bottom: 20px;
  position: relative;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #34495e;
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #3498db;
}

.char-count {
  position: absolute;
  right: 8px;
  bottom: 8px;
  font-size: 12px;
  color: #7f8c8d;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 0;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
}

.checkbox-text {
  color: #34495e;
  font-weight: 500;
}

.options-container,
.scopes-container {
  margin-bottom: 15px;
}

.option-item,
.scope-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.option-number {
  width: 30px;
  height: 30px;
  background: #3498db;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.scope-item select {
  flex: 1;
  margin-bottom: 0;
}

.scope-item input {
  flex: 1;
  margin-bottom: 0;
}

.btn-remove {
  width: 30px;
  height: 30px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
  transition: background-color 0.3s ease;
}

.btn-remove:hover {
  background: #c0392b;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn.primary {
  background: #3498db;
  color: white;
}

.btn.primary:hover {
  background: #2980b9;
}

.btn.primary:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.btn.secondary {
  background: #ecf0f1;
  color: #34495e;
}

.btn.secondary:hover {
  background: #d5dbdb;
}

.btn-icon {
  font-size: 16px;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.error-message {
  background: #fee;
  color: #e74c3c;
  padding: 15px 20px;
  border-radius: 8px;
  margin: 20px 0;
  border-left: 4px solid #e74c3c;
}

.success-message {
  background: #eef;
  color: #27ae60;
  padding: 15px 20px;
  border-radius: 8px;
  margin: 20px 0;
  border-left: 4px solid #27ae60;
}

@media (max-width: 768px) {
  .vote-management {
    padding: 10px;
  }

  .page-header {
    padding: 20px;
  }

  .create-form-container {
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style>

<template>
  <div class="vote-management">
    <div class="page-header">
      <h1>投票管理</h1>
      <p class="subtitle">创建和管理社区投票活动</p>
    </div>

    <div class="management-content">
      <!-- 功能切换按钮 -->
      <div class="function-tabs">
        <button
          @click="currentFunction = 'create'"
          :class="['tab-btn', { active: currentFunction === 'create' }]"
        >
          <span class="tab-icon">➕</span>
          新建投票
        </button>
        <button
          @click="currentFunction = 'view'"
          :class="['tab-btn', { active: currentFunction === 'view' }]"
        >
          <span class="tab-icon">📋</span>
          查看投票
        </button>
      </div>

      <!-- 创建投票功能 -->
      <div v-if="currentFunction === 'create'" class="create-vote-section">
        <div class="section-header">
          <h2>创建新投票</h2>
          <button
            v-if="!showCreateForm"
            @click="showCreateForm = true"
            class="btn primary"
          >
            <span class="btn-icon">➕</span>
            创建投票
          </button>
        </div>

        <div v-if="showCreateForm" class="create-form-container">
          <form @submit.prevent="submitVote" class="vote-form">
            <!-- 基本信息 -->
            <div class="form-section">
              <h3>基本信息</h3>
              
              <div class="form-group">
                <label for="title">投票标题 *</label>
                <input
                  id="title"
                  v-model="voteForm.title"
                  type="text"
                  placeholder="请输入投票标题"
                  maxlength="100"
                  required
                />
                <div class="char-count">{{ voteForm.title.length }}/100</div>
              </div>

              <div class="form-group">
                <label for="description">投票描述 *</label>
                <textarea
                  id="description"
                  v-model="voteForm.description"
                  placeholder="请输入投票描述"
                  maxlength="500"
                  rows="4"
                  required
                ></textarea>
                <div class="char-count">{{ voteForm.description.length }}/500</div>
              </div>
            </div>

            <!-- 时间设置 -->
            <div class="form-section">
              <h3>时间设置</h3>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="startTime">开始时间 *</label>
                  <input
                    id="startTime"
                    v-model="voteForm.startTime"
                    type="datetime-local"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="endTime">结束时间 *</label>
                  <input
                    id="endTime"
                    v-model="voteForm.endTime"
                    type="datetime-local"
                    required
                  />
                </div>
              </div>
            </div>

            <!-- 投票设置 -->
            <div class="form-section">
              <h3>投票设置</h3>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="maxChoices">最大选择数 *</label>
                  <input
                    id="maxChoices"
                    v-model.number="voteForm.maxChoices"
                    type="number"
                    min="1"
                    required
                  />
                </div>

                <div class="form-group">
                  <label class="checkbox-label">
                    <input
                      v-model="voteForm.isAnonymous"
                      type="checkbox"
                    />
                    <span class="checkbox-text">匿名投票</span>
                  </label>
                </div>
              </div>
            </div>

            <!-- 投票选项 -->
            <div class="form-section">
              <h3>投票选项</h3>
              
              <div class="options-container">
                <div
                  v-for="(option, index) in voteForm.options"
                  :key="index"
                  class="option-item"
                >
                  <div class="option-number">{{ index + 1 }}</div>
                  <input
                    v-model="option.content"
                    type="text"
                    placeholder="请输入选项内容"
                    maxlength="100"
                    required
                  />
                  <button
                    v-if="voteForm.options.length > 2"
                    @click="removeOption(index)"
                    type="button"
                    class="btn-remove"
                  >
                    ✕
                  </button>
                </div>
              </div>

              <button
                @click="addOption"
                type="button"
                class="btn secondary"
              >
                <span class="btn-icon">➕</span>
                添加选项
              </button>
            </div>

            <!-- 投票范围 -->
            <div class="form-section">
              <h3>投票范围</h3>
              
              <div class="scopes-container">
                <div
                  v-for="(scope, index) in voteForm.scopes"
                  :key="index"
                  class="scope-item"
                >
                  <select v-model="scope.targetType" required>
                    <option value="">请选择范围类型</option>
                    <option value="1">社区</option>
                    <option value="2">网格</option>
                    <option value="3">楼栋</option>
                  </select>
                  
                  <input
                    v-model.number="scope.targetId"
                    type="number"
                    placeholder="请输入ID"
                    min="1"
                    required
                  />
                  
                  <button
                    v-if="voteForm.scopes.length > 1"
                    @click="removeScope(index)"
                    type="button"
                    class="btn-remove"
                  >
                    ✕
                  </button>
                </div>
              </div>

              <button
                @click="addScope"
                type="button"
                class="btn secondary"
              >
                <span class="btn-icon">➕</span>
                添加范围
              </button>
            </div>

            <!-- 表单操作 -->
            <div class="form-actions">
              <button
                @click="cancelCreate"
                type="button"
                class="btn secondary"
              >
                取消
              </button>
              <button
                type="submit"
                class="btn primary"
                :disabled="isSubmitting"
              >
                <span v-if="isSubmitting">创建中...</span>
                <span v-else>创建投票</span>
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- 查看投票功能 -->
      <div v-if="currentFunction === 'view'" class="view-votes-section">
        <!-- 加载状态 -->
        <div v-if="isLoadingVotes" class="loading-container">
          <div class="loading-spinner"></div>
          <p>正在加载投票数据...</p>
        </div>

        <!-- 错误提示 -->
        <div v-else-if="voteListError" class="error-message">
          {{ voteListError }}
          <div class="error-actions">
            <button @click="loadVoteList" class="retry-btn">重试</button>
          </div>
        </div>

        <!-- 投票列表 -->
        <div v-else-if="voteList.length > 0" class="votes-container">
          <div class="section-header">
            <h2>现有投票列表</h2>
            <button @click="loadVoteList" class="btn secondary">
              <span class="btn-icon">🔄</span>
              刷新
            </button>
          </div>

          <div class="votes-list">
            <div
              v-for="vote in voteList"
              :key="vote.id"
              class="vote-card"
            >
              <div class="vote-header">
                <h3>{{ vote.title }}</h3>
                <div class="vote-status">
                  <span :class="['status-badge', getVoteStatus(vote).class]">
                    {{ getVoteStatus(vote).text }}
                  </span>
                </div>
              </div>

              <div class="vote-info">
                <p class="vote-description">{{ vote.description }}</p>

                <div class="vote-meta">
                  <div class="meta-item">
                    <span class="meta-label">开始时间:</span>
                    <span class="meta-value">{{ formatDateTime(vote.startTime) }}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">结束时间:</span>
                    <span class="meta-value">{{ formatDateTime(vote.endTime) }}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">最大选择:</span>
                    <span class="meta-value">{{ vote.maxChoices }}项</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">投票方式:</span>
                    <span class="meta-value">{{ vote.isAnonymous ? '匿名' : '实名' }}</span>
                  </div>
                </div>
              </div>

              <!-- 投票结果展示 -->
              <div class="vote-results">
                <h4>投票结果</h4>
                <div class="results-list">
                  <div
                    v-for="option in vote.options"
                    :key="option.sortOrder"
                    class="result-item"
                  >
                    <div class="option-info">
                      <span class="option-content">{{ option.content }}</span>
                      <span class="option-votes">{{ option.number }}票</span>
                    </div>
                    <div class="progress-bar">
                      <div
                        class="progress-fill"
                        :style="{ width: getOptionPercentage(option, vote.options) + '%' }"
                      ></div>
                    </div>
                    <span class="percentage">{{ getOptionPercentage(option, vote.options).toFixed(1) }}%</span>
                  </div>
                </div>
              </div>

              <!-- 投票范围信息 -->
              <div class="vote-scopes">
                <h4>投票范围</h4>
                <div class="scopes-list">
                  <span
                    v-for="scope in vote.scopes"
                    :key="`${scope.targetType}-${scope.targetId}`"
                    class="scope-tag"
                  >
                    {{ getScopeText(scope) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <div class="empty-icon">🗳️</div>
          <h3>暂无投票活动</h3>
          <p>当前没有投票活动，请创建新的投票。</p>
        </div>
      </div>

      <!-- 错误提示 -->
      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <!-- 成功提示 -->
      <div v-if="successMessage" class="success-message">
        {{ successMessage }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { createVote, validateVoteData, getVoteList } from '../../services/voteApi.js';

// 响应式数据
const currentFunction = ref('create'); // 'create' 或 'view'
const showCreateForm = ref(false);
const isSubmitting = ref(false);
const errorMessage = ref('');
const successMessage = ref('');

// 查看投票相关数据
const isLoadingVotes = ref(false);
const voteListError = ref('');
const voteList = ref([]);

// 投票表单数据
const voteForm = reactive({
  title: '',
  description: '',
  startTime: '',
  endTime: '',
  maxChoices: 1,
  isAnonymous: false,
  options: [
    { sortOrder: 1, content: '' },
    { sortOrder: 2, content: '' }
  ],
  scopes: [
    { targetType: '', targetId: null }
  ]
});

// 添加选项
const addOption = () => {
  voteForm.options.push({
    sortOrder: voteForm.options.length + 1,
    content: ''
  });
};

// 删除选项
const removeOption = (index) => {
  if (voteForm.options.length > 2) {
    voteForm.options.splice(index, 1);
    // 重新排序
    voteForm.options.forEach((option, idx) => {
      option.sortOrder = idx + 1;
    });
  }
};

// 添加投票范围
const addScope = () => {
  voteForm.scopes.push({
    targetType: '',
    targetId: null
  });
};

// 删除投票范围
const removeScope = (index) => {
  if (voteForm.scopes.length > 1) {
    voteForm.scopes.splice(index, 1);
  }
};

// 取消创建
const cancelCreate = () => {
  showCreateForm.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  voteForm.title = '';
  voteForm.description = '';
  voteForm.startTime = '';
  voteForm.endTime = '';
  voteForm.maxChoices = 1;
  voteForm.isAnonymous = false;
  voteForm.options = [
    { sortOrder: 1, content: '' },
    { sortOrder: 2, content: '' }
  ];
  voteForm.scopes = [
    { targetType: '', targetId: null }
  ];
  errorMessage.value = '';
  successMessage.value = '';
};

// 提交投票
const submitVote = async () => {
  if (isSubmitting.value) return;

  errorMessage.value = '';
  successMessage.value = '';

  // 准备提交数据
  const submitData = {
    title: voteForm.title.trim(),
    description: voteForm.description.trim(),
    startTime: voteForm.startTime.replace('T', ' ') + ':00',
    endTime: voteForm.endTime.replace('T', ' ') + ':00',
    maxChoices: voteForm.maxChoices,
    isAnonymous: voteForm.isAnonymous ? 1 : 0,
    options: voteForm.options.map(option => ({
      sortOrder: option.sortOrder,
      content: option.content.trim()
    })),
    scopes: voteForm.scopes.map(scope => ({
      targetType: parseInt(scope.targetType),
      targetId: scope.targetId
    }))
  };

  // 验证数据
  const validation = validateVoteData(submitData);
  if (!validation.isValid) {
    errorMessage.value = validation.errors.join('; ');
    return;
  }

  isSubmitting.value = true;

  try {
    console.log('🔧 提交投票数据:', submitData);

    const result = await createVote(submitData);

    if (result.success) {
      successMessage.value = '投票创建成功！';
      showCreateForm.value = false;
      resetForm();
      console.log('✅ 投票创建成功');
    } else {
      throw new Error(result.message || '投票创建失败');
    }

  } catch (error) {
    console.error('❌ 投票创建失败:', error);
    errorMessage.value = error.message || '投票创建失败，请稍后重试';
  } finally {
    isSubmitting.value = false;
  }
};

// 加载投票列表
const loadVoteList = async () => {
  isLoadingVotes.value = true;
  voteListError.value = '';

  try {
    console.log('🔧 开始加载投票列表');
    const result = await getVoteList();

    if (result.success) {
      voteList.value = result.data || [];
      console.log('✅ 投票列表加载成功，共', voteList.value.length, '个投票');
    } else {
      throw new Error(result.message || '投票列表加载失败');
    }
  } catch (error) {
    console.error('❌ 投票列表加载失败:', error);
    voteListError.value = error.message || '投票列表加载失败，请稍后重试';
  } finally {
    isLoadingVotes.value = false;
  }
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';

  const date = new Date(dateTimeStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取投票状态
const getVoteStatus = (vote) => {
  const now = new Date();
  const startTime = new Date(vote.startTime);
  const endTime = new Date(vote.endTime);

  if (now < startTime) {
    return { text: '未开始', class: 'pending' };
  } else if (now > endTime) {
    return { text: '已结束', class: 'ended' };
  } else {
    return { text: '进行中', class: 'active' };
  }
};

// 计算选项百分比
const getOptionPercentage = (option, allOptions) => {
  const totalVotes = allOptions.reduce((sum, opt) => sum + (opt.number || 0), 0);
  if (totalVotes === 0) return 0;
  return ((option.number || 0) / totalVotes) * 100;
};

// 获取范围文本
const getScopeText = (scope) => {
  const typeMap = {
    1: '社区',
    2: '网格',
    3: '楼栋'
  };
  return `${typeMap[scope.targetType] || '未知'}${scope.targetId}`;
};

// 监听功能切换
watch(currentFunction, (newFunction) => {
  if (newFunction === 'view' && voteList.value.length === 0) {
    loadVoteList();
  }
});

// 生命周期
onMounted(() => {
  // 默认不加载投票列表，只有切换到查看功能时才加载
});
</script>

<style scoped>
.vote-management {
  background: #f5f7fa;
  min-height: 100vh;
  padding: 20px;
}

.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
}

.management-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* 功能切换标签 */
.function-tabs {
  display: flex;
  gap: 0;
  margin-bottom: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  border: none;
  background: white;
  color: #7f8c8d;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.tab-btn.active {
  background: #3498db;
  color: white;
  border-bottom-color: #2980b9;
}

.tab-icon {
  font-size: 18px;
}

.create-vote-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

.section-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.create-form-container {
  padding: 30px;
}

.vote-form {
  max-width: 800px;
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h3 {
  margin: 0 0 20px 0;
  color: #34495e;
  font-size: 18px;
  font-weight: 600;
}

.form-group {
  margin-bottom: 20px;
  position: relative;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #34495e;
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #3498db;
}

.char-count {
  position: absolute;
  right: 8px;
  bottom: 8px;
  font-size: 12px;
  color: #7f8c8d;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 0;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
}

.checkbox-text {
  color: #34495e;
  font-weight: 500;
}

.options-container,
.scopes-container {
  margin-bottom: 15px;
}

.option-item,
.scope-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.option-number {
  width: 30px;
  height: 30px;
  background: #3498db;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.scope-item select {
  flex: 1;
  margin-bottom: 0;
}

.scope-item input {
  flex: 1;
  margin-bottom: 0;
}

.btn-remove {
  width: 30px;
  height: 30px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
  transition: background-color 0.3s ease;
}

.btn-remove:hover {
  background: #c0392b;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn.primary {
  background: #3498db;
  color: white;
}

.btn.primary:hover {
  background: #2980b9;
}

.btn.primary:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.btn.secondary {
  background: #ecf0f1;
  color: #34495e;
}

.btn.secondary:hover {
  background: #d5dbdb;
}

.btn-icon {
  font-size: 16px;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.error-message {
  background: #fee;
  color: #e74c3c;
  padding: 15px 20px;
  border-radius: 8px;
  margin: 20px 0;
  border-left: 4px solid #e74c3c;
}

.success-message {
  background: #eef;
  color: #27ae60;
  padding: 15px 20px;
  border-radius: 8px;
  margin: 20px 0;
  border-left: 4px solid #27ae60;
}

/* 查看投票功能样式 */
.view-votes-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.loading-container {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-btn {
  margin-top: 15px;
  padding: 10px 20px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
}

.retry-btn:hover {
  background: #2980b9;
}

.error-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  justify-content: center;
}

.votes-list {
  display: grid;
  gap: 20px;
  padding: 20px;
}

.vote-card {
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.vote-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.vote-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.vote-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.pending {
  background: #f39c12;
  color: white;
}

.status-badge.active {
  background: #27ae60;
  color: white;
}

.status-badge.ended {
  background: #95a5a6;
  color: white;
}

.vote-info {
  padding: 20px;
}

.vote-description {
  margin: 0 0 20px 0;
  color: #34495e;
  line-height: 1.6;
}

.vote-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.meta-item {
  display: flex;
  justify-content: space-between;
}

.meta-label {
  color: #7f8c8d;
  font-weight: 500;
}

.meta-value {
  color: #2c3e50;
  font-weight: 600;
}

.vote-results {
  padding: 20px;
  border-top: 1px solid #e9ecef;
}

.vote-results h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.results-list {
  display: grid;
  gap: 15px;
}

.result-item {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 15px;
  align-items: center;
}

.option-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-content {
  color: #2c3e50;
  font-weight: 500;
}

.option-votes {
  color: #7f8c8d;
  font-size: 14px;
  margin-left: 10px;
}

.progress-bar {
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  min-width: 100px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  transition: width 0.3s ease;
}

.percentage {
  color: #34495e;
  font-weight: 600;
  font-size: 14px;
  min-width: 50px;
  text-align: right;
}

.vote-scopes {
  padding: 20px;
  border-top: 1px solid #e9ecef;
}

.vote-scopes h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.scopes-list {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.scope-tag {
  padding: 6px 12px;
  background: #ecf0f1;
  color: #34495e;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 24px;
}

.empty-state p {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
}

@media (max-width: 768px) {
  .vote-management {
    padding: 10px;
  }

  .page-header {
    padding: 20px;
  }

  .create-form-container {
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .form-actions {
    flex-direction: column;
  }

  .function-tabs {
    flex-direction: column;
  }

  .tab-btn {
    border-bottom: none;
    border-right: 3px solid transparent;
  }

  .tab-btn.active {
    border-right-color: #2980b9;
    border-bottom-color: transparent;
  }

  .vote-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .vote-info,
  .vote-results,
  .vote-scopes {
    padding: 15px;
  }

  .vote-meta {
    grid-template-columns: 1fr;
  }

  .meta-item {
    flex-direction: column;
    gap: 5px;
  }

  .result-item {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .scopes-list {
    justify-content: center;
  }
}
</style>

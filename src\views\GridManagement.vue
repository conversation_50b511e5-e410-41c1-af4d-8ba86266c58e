<template>
  <div class="grid-management">
    <div class="page-header">
      <h1>网格管理</h1>
      <p class="subtitle">网格边界编辑、信息维护与网格合并拆分</p>
    </div>

    <div class="management-content">
      <!-- 操作工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <button
            class="btn primary"
            @click.stop.prevent="createGrid"
            type="button"
            style="pointer-events: auto; z-index: 1000;"
          >
            <span class="btn-icon">➕</span>
            新建网格
          </button>

          <!-- 临时测试按钮 -->
          <button
            class="btn secondary"
            @click="() => dialog.showInfo('测试按钮工作正常！', '测试')"
            type="button"
            style="margin-left: 10px;"
          >
            🧪 测试
          </button>

          <!-- 测试导航按钮 -->
          <button
            class="btn info"
            @click="testNavigation"
            type="button"
            style="margin-left: 10px; background: #17a2b8; color: white;"
          >
            🧭 测试导航
          </button>
          <button class="btn secondary" @click="importGrids">
            <span class="btn-icon">📥</span>
            导入网格
          </button>
          <button class="btn secondary" @click="exportGrids">
            <span class="btn-icon">📤</span>
            导出网格
          </button>
        </div>
        <div class="toolbar-center">
          <!-- GIS功能快速入口 -->
          <div class="gis-nav">
            <button class="nav-btn" @click="navigateToGISManagement">
              <span class="nav-icon">🗺️</span>
              GIS管理系统
            </button>
            <button class="nav-btn" @click="navigateToEventMap">
              <span class="nav-icon">📍</span>
              事件地图
            </button>
            <button class="nav-btn" @click="navigateToPatrolManagement">
              <span class="nav-icon">🚶</span>
              巡查管理
            </button>
            <button class="nav-btn" @click="navigateToDataOverview">
              <span class="nav-icon">📊</span>
              数据总览
            </button>
          </div>
        </div>
        <div class="toolbar-right">
          <div class="search-box">
            <input
              type="text"
              v-model="searchQuery"
              placeholder="搜索网格名称或负责人..."
              class="search-input"
            >
            <button class="search-btn">🔍</button>
          </div>
          <select v-model="statusFilter" class="filter-select">
            <option value="">全部状态</option>
            <option value="active">活跃</option>
            <option value="inactive">非活跃</option>
          </select>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <div class="content-layout">
          <!-- 网格列表 -->
          <div class="grid-list-section">
            <div class="section-header">
              <h2>网格列表</h2>
              <span class="grid-count">共 {{ filteredGrids.length }} 个网格</span>
            </div>
            
            <div class="grid-list">
              <!-- 加载状态 -->
              <div v-if="isLoadingGrids" class="loading-message">
                <div class="loading-spinner"></div>
                <p>正在加载网格数据...</p>
              </div>

              <!-- 空状态 -->
              <div v-else-if="filteredGrids.length === 0" class="empty-message">
                <p>暂无网格数据</p>
                <button class="btn primary" @click="createGrid">创建第一个网格</button>
              </div>

              <!-- 网格列表 -->
              <div
                v-else
                v-for="grid in filteredGrids"
                :key="grid.id"
                :class="['grid-item', { selected: selectedGrid?.id === grid.id }]"
                @click="selectGrid(grid)"
              >
                <div class="grid-header">
                  <h3>{{ grid.name }}</h3>
                  <span :class="['status-badge', grid.status]">
                    {{ getStatusText(grid.status) }}
                  </span>
                </div>
                <div class="grid-info">
                  <div class="info-item">
                    <span class="label">负责人:</span>
                    <span class="value">{{ grid.manager }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">面积:</span>
                    <span class="value">{{ grid.area.toLocaleString() }}m²</span>
                  </div>
                  <div class="info-item">
                    <span class="label">户数:</span>
                    <span class="value">{{ grid.households }}户</span>
                  </div>
                  <div class="info-item">
                    <span class="label">人口:</span>
                    <span class="value">{{ grid.population }}人</span>
                  </div>
                </div>
                <div class="grid-actions">
                  <button class="action-btn edit" @click.stop="editGrid(grid)">
                    <span class="btn-icon">✏️</span>
                    编辑
                  </button>
                  <button class="action-btn view" @click.stop="viewGridDetails(grid)">
                    <span class="btn-icon">👁️</span>
                    详情
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 地图区域 -->
          <div class="map-section">
            <div class="section-header">
              <h2>网格地图</h2>
              <div class="map-tools">
                <button
                  :class="['tool-btn', { active: drawingMode === 'polygon' }]"
                  @click.stop.prevent="toggleDrawingMode('polygon')"
                  type="button"
                  style="pointer-events: auto; z-index: 1000;"
                >
                  <span class="btn-icon">⬟</span>
                  绘制网格
                </button>
                <button 
                  :class="['tool-btn', { active: editingMode }]"
                  @click="toggleEditingMode"
                >
                  <span class="btn-icon">✏️</span>
                  编辑边界
                </button>
                <button class="tool-btn" @click="clearSelection">
                  <span class="btn-icon">🧹</span>
                  清除选择
                </button>
              </div>
            </div>
            
            <div class="map-container">
              <MapComponent
                ref="mapComponent"
                :height="'500px'"
                :center="mapCenter"
                :zoom="16"
                :markers="mapMarkers"
                :polygons="mapPolygons"
                :show-drawing-tools="true"
                @marker-click="onMarkerClick"
                @polygon-click="onPolygonClick"
                @draw-created="onDrawCreated"
              />
            </div>
          </div>
        </div>

        <!-- 网格详情面板 -->
        <div v-if="selectedGrid" class="details-panel">
          <div class="panel-header">
            <h3>{{ selectedGrid.name }} - 详细信息</h3>
            <button class="close-btn" @click="closeDetails">×</button>
          </div>
          <div class="panel-content">
            <div class="detail-tabs">
              <button 
                v-for="tab in detailTabs" 
                :key="tab.key"
                :class="['tab-btn', { active: activeDetailTab === tab.key }]"
                @click="activeDetailTab = tab.key"
              >
                {{ tab.name }}
              </button>
            </div>
            
            <div class="tab-content">
              <!-- 基本信息 -->
              <div v-if="activeDetailTab === 'basic'" class="basic-info">
                <div class="info-grid">
                  <div class="info-item">
                    <label>网格名称:</label>
                    <span>{{ selectedGrid.name }}</span>
                  </div>
                  <div class="info-item">
                    <label>网格编号:</label>
                    <span>{{ selectedGrid.code }}</span>
                  </div>
                  <div class="info-item">
                    <label>负责人:</label>
                    <span>{{ selectedGrid.manager }}</span>
                  </div>
                  <div class="info-item">
                    <label>联系电话:</label>
                    <span>{{ selectedGrid.phone }}</span>
                  </div>
                  <div class="info-item">
                    <label>网格面积:</label>
                    <span>{{ selectedGrid.area.toLocaleString() }}m²</span>
                  </div>
                  <div class="info-item">
                    <label>户数统计:</label>
                    <span>{{ selectedGrid.households }}户</span>
                  </div>
                  <div class="info-item">
                    <label>人口统计:</label>
                    <span>{{ selectedGrid.population }}人</span>
                  </div>
                  <div class="info-item">
                    <label>状态:</label>
                    <span :class="['status-text', selectedGrid.status]">
                      {{ getStatusText(selectedGrid.status) }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 统计数据 -->
              <div v-if="activeDetailTab === 'statistics'" class="statistics-info">
                <div class="stats-grid">
                  <div class="stat-item">
                    <div class="stat-number">{{ selectedGrid.events || 0 }}</div>
                    <div class="stat-label">本月事件</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">{{ selectedGrid.patrols || 0 }}</div>
                    <div class="stat-label">巡查次数</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">{{ selectedGrid.complaints || 0 }}</div>
                    <div class="stat-label">投诉数量</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">{{ selectedGrid.satisfaction || 0 }}%</div>
                    <div class="stat-label">满意度</div>
                  </div>
                </div>
              </div>

              <!-- 操作历史 -->
              <div v-if="activeDetailTab === 'history'" class="history-info">
                <div class="history-list">
                  <div v-for="record in selectedGrid.history" :key="record.id" class="history-item">
                    <div class="history-time">{{ formatDate(record.time) }}</div>
                    <div class="history-action">{{ record.action }}</div>
                    <div class="history-user">{{ record.user }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 网格编辑对话框 -->
    <div v-if="showEditDialog" class="dialog-overlay" @click="closeEditDialog">
      <div class="dialog" @click.stop>
        <div class="dialog-header">
          <h3>{{ editingGrid.id ? '编辑网格' : '新建网格' }}</h3>
          <button class="close-btn" @click="closeEditDialog">×</button>
        </div>
        <div class="dialog-content">
          <form @submit.prevent="saveGrid">
            <div class="form-grid">
              <div class="form-group">
                <label>网格名称</label>
                <input type="text" v-model="editingGrid.name" required>
              </div>
              <div class="form-group">
                <label>网格编号</label>
                <input type="text" v-model="editingGrid.code" required>
              </div>
              <div class="form-group">
                <label>负责人</label>
                <input type="text" v-model="editingGrid.manager" required>
              </div>
              <div class="form-group">
                <label>联系电话</label>
                <input type="tel" v-model="editingGrid.phone" required>
              </div>
              <div class="form-group">
                <label>网格面积(m²)</label>
                <input type="number" v-model="editingGrid.area" required>
              </div>
              <div class="form-group">
                <label>状态</label>
                <select v-model="editingGrid.status" required>
                  <option value="active">活跃</option>
                  <option value="inactive">非活跃</option>
                </select>
              </div>
            </div>
            <div class="form-actions">
              <button type="button" class="btn secondary" @click="closeEditDialog">取消</button>
              <button type="submit" class="btn primary">保存</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, inject } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '../stores/userStore.js';
import MapComponent from '../components/MapComponent.vue';
import { getAllGrids } from '../services/gridApi.js';

const router = useRouter();
const userStore = useUserStore();
const dialog = inject('dialog'); // 注入对话框服务

// 响应式数据
const mapComponent = ref(null);
const searchQuery = ref('');
const statusFilter = ref('');
const selectedGrid = ref(null);
const activeDetailTab = ref('basic');
const drawingMode = ref(null);
const editingMode = ref(false);
const showEditDialog = ref(false);
const editingGrid = reactive({});

// 地图配置
const mapCenter = [117.198612, 31.774164]; // 社区管理系统地图中心点，高德地图格式：[经度, 纬度]

// 详情标签页
const detailTabs = [
  { key: 'basic', name: '基本信息' },
  { key: 'statistics', name: '统计数据' },
  { key: 'history', name: '操作历史' }
];

// 工具函数：计算多边形面积（简单估算）
const calculatePolygonArea = (coordinates) => {
  if (!coordinates || coordinates.length < 3) return 0;

  let area = 0;
  const n = coordinates.length;

  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    area += coordinates[i][0] * coordinates[j][1];
    area -= coordinates[j][0] * coordinates[i][1];
  }

  return Math.abs(area / 2) * 111000 * 111000; // 粗略转换为平方米
};

// 工具函数：计算多边形中心点
const calculatePolygonCenter = (coordinates) => {
  if (!coordinates || coordinates.length === 0) {
    return { lat: 31.774164, lng: 117.198612 }; // 默认中心点
  }

  let sumLat = 0, sumLng = 0;
  coordinates.forEach(coord => {
    sumLng += coord[0]; // 经度
    sumLat += coord[1]; // 纬度
  });

  return {
    lat: sumLat / coordinates.length,
    lng: sumLng / coordinates.length
  };
};

/**
 * 转换后端网格数据为GridManagement页面格式
 * @param {Object} backendGrid - 后端网格数据
 * @returns {Object} GridManagement页面网格数据格式
 */
const transformBackendGridToManagement = (backendGrid) => {
  try {
    // 新格式：后端直接返回坐标对象数组
    const coordinateObjects = backendGrid.coordinates;

    // 转换为前端期望的坐标数组格式 [lng, lat]
    const coordinates = coordinateObjects.map(coord => [
      coord.longitude,
      coord.latitude
    ]);

    // 计算多边形面积
    const area = calculatePolygonArea(coordinates);

    return {
      id: backendGrid.id,
      name: backendGrid.gridName,
      code: `GRID-${backendGrid.id.toString().padStart(3, '0')}`, // 生成网格编号
      manager: '待分配', // 后端暂时没有负责人信息
      phone: '', // 后端暂时没有电话信息
      area: Math.round(area),
      households: 0, // 后端暂时没有户数信息
      population: 0, // 后端暂时没有人口信息
      status: 'active', // 后端暂时没有状态信息，使用默认值
      events: 0, // 后端暂时没有事件统计
      patrols: 0, // 后端暂时没有巡查统计
      complaints: 0, // 后端暂时没有投诉统计
      satisfaction: 0, // 后端暂时没有满意度统计
      coordinates: coordinates,
      communityId: backendGrid.communityId,
      responsibleId: backendGrid.responsibleId,
      createdAt: new Date(backendGrid.createTime),
      updatedAt: new Date(backendGrid.updateTime),
      history: [
        {
          id: 1,
          time: new Date(backendGrid.createTime),
          action: '创建网格',
          user: '系统'
        }
      ]
    };
  } catch (error) {
    console.error('转换网格数据失败:', error, backendGrid);
    return null;
  }
};

/**
 * 从后端加载网格数据
 */
const loadGridsFromBackend = async () => {
  try {
    console.log('🔧 GridManagement: 开始从后端加载网格数据...');

    const response = await getAllGrids();

    if (response.success && response.data) {
      console.log('✅ GridManagement: 后端网格数据获取成功:', response.data.length, '个网格');

      // 转换数据格式
      const transformedGrids = response.data
        .map(transformBackendGridToManagement)
        .filter(grid => grid !== null); // 过滤掉转换失败的数据

      console.log('✅ GridManagement: 网格数据转换完成:', transformedGrids.length, '个有效网格');
      return transformedGrids;
    } else {
      console.warn('⚠️ GridManagement: 后端返回数据为空，使用空数组');
      return [];
    }
  } catch (error) {
    console.error('❌ GridManagement: 从后端加载网格数据失败:', error);
    return [];
  }
};

// 网格数据
const grids = ref([]);
const isLoadingGrids = ref(false);

// 地图数据
const mapMarkers = ref([]);

// 计算属性：从网格数据生成地图多边形
const mapPolygons = computed(() => {
  return grids.value.map(grid => {
    const color = grid.status === 'active' ? '#28a745' : '#6c757d';
    const statusText = grid.status === 'active' ? '活跃' : '非活跃';

    return {
      id: grid.id,
      title: grid.name,
      coordinates: grid.coordinates || [],
      color: color,
      fillColor: color,
      fillOpacity: selectedGrid.value?.id === grid.id ? 0.4 : 0.2,
      strokeWeight: selectedGrid.value?.id === grid.id ? 3 : 2,
      popup: `<div><h4>${grid.name}</h4><p>面积：${grid.area}m²</p><p>负责人：${grid.manager}</p><p>状态：${statusText}</p></div>`,
      properties: {
        gridId: grid.id,
        name: grid.name,
        area: grid.area,
        manager: grid.manager,
        status: grid.status
      }
    };
  });
});

// 计算属性
const filteredGrids = computed(() => {
  let filtered = grids.value;
  
  if (searchQuery.value) {
    filtered = filtered.filter(grid => 
      grid.name.includes(searchQuery.value) || 
      grid.manager.includes(searchQuery.value)
    );
  }
  
  if (statusFilter.value) {
    filtered = filtered.filter(grid => grid.status === statusFilter.value);
  }
  
  return filtered;
});

// 方法
const selectGrid = (grid) => {
  selectedGrid.value = grid;
  // 在地图上高亮显示选中的网格
};

const getStatusText = (status) => {
  return status === 'active' ? '活跃' : '非活跃';
};

// 简单的测试函数
const testNavigation = async () => {
  console.log('🧪 测试导航函数被调用');

  try {
    console.log('🧭 尝试跳转到GIS管理页面...');
    console.log('📍 当前路由:', router.currentRoute.value.path);
    console.log('🎯 目标路由: /property/gis-management');

    const result = await router.push({
      path: '/property/gis-management',
      query: {
        tab: 'grid',
        action: 'test'
      }
    });

    console.log('✅ 测试路由跳转结果:', result);
    console.log('📍 跳转后路由:', router.currentRoute.value.path);

    if (router.currentRoute.value.path === '/property/gis-management') {
      dialog.showSuccess('🎉 测试成功！已跳转到GIS管理页面', '测试成功');
    } else {
      dialog.showWarning('⚠️ 测试失败：路由跳转可能未成功', '测试失败');
    }

  } catch (error) {
    console.error('❌ 测试路由跳转失败:', error);
    dialog.showError('测试失败: ' + error.message, '测试失败');
  }
};

const createGrid = async () => {
  console.log('🔧 点击新建网格按钮');

  try {
    // 检查用户认证状态
    console.log('👤 用户认证状态:', userStore.isAuthenticated);
    console.log('👤 用户类型:', userStore.userType);
    console.log('👤 用户信息:', userStore.user);

    // 如果用户未认证，先模拟登录
    if (!userStore.isAuthenticated) {
      console.log('⚠️ 用户未认证，模拟登录...');
      userStore.login({
        id: 1,
        name: '测试用户',
        email: '<EMAIL>'
      }, 'property');
      console.log('✅ 模拟登录完成');
    }

    // 跳转到GIS管理页面并激活绘制模式
    console.log('🧭 准备跳转到GIS管理页面...');
    console.log('📍 当前路由:', router.currentRoute.value.path);
    console.log('🎯 目标路由: /property/gis-management');

    // 使用 await 确保路由跳转完成
    const result = await router.push({
      path: '/property/gis-management',
      query: {
        tab: 'grid',
        action: 'create'
      }
    });

    console.log('✅ 路由跳转结果:', result);
    console.log('📍 跳转后路由:', router.currentRoute.value.path);

    // 如果跳转成功，显示成功消息
    if (router.currentRoute.value.path === '/property/gis-management') {
      console.log('🎉 成功跳转到GIS管理页面！');
    } else {
      console.warn('⚠️ 路由跳转可能未成功');
    }

  } catch (error) {
    console.error('❌ 路由跳转失败:', error);
    dialog.showError('跳转失败: ' + error.message, '操作失败');

    // 尝试备用跳转方式
    console.log('🔄 尝试备用跳转方式...');
    try {
      window.location.href = '/property/gis-management?tab=grid&action=create';
    } catch (fallbackError) {
      console.error('❌ 备用跳转也失败:', fallbackError);
    }
  }
};

const editGrid = (grid) => {
  Object.assign(editingGrid, { ...grid });
  showEditDialog.value = true;
};

const viewGridDetails = (grid) => {
  selectedGrid.value = grid;
  activeDetailTab.value = 'basic';
};



const saveGrid = () => {
  if (editingGrid.id) {
    // 更新现有网格
    const index = grids.value.findIndex(g => g.id === editingGrid.id);
    if (index > -1) {
      grids.value[index] = { ...editingGrid };
    }
  } else {
    // 创建新网格
    const newGrid = {
      ...editingGrid,
      id: Date.now(),
      households: 0,
      population: 0,
      events: 0,
      patrols: 0,
      complaints: 0,
      satisfaction: 0,
      history: []
    };
    grids.value.push(newGrid);
  }
  closeEditDialog();
};

const closeEditDialog = () => {
  showEditDialog.value = false;
  Object.keys(editingGrid).forEach(key => delete editingGrid[key]);
};

const closeDetails = () => {
  selectedGrid.value = null;
};

const toggleDrawingMode = async (mode) => {
  console.log('🔧 点击绘制网格按钮, mode:', mode);
  dialog.showInfo(`绘制网格按钮被点击了！模式: ${mode}`, '绘制模式'); // 临时测试

  try {
    // 检查用户认证状态
    console.log('👤 用户认证状态:', userStore.isAuthenticated);
    console.log('👤 用户类型:', userStore.userType);

    // 如果用户未认证，先模拟登录
    if (!userStore.isAuthenticated) {
      console.log('⚠️ 用户未认证，模拟登录...');
      userStore.login({
        id: 1,
        name: '测试用户',
        email: '<EMAIL>'
      }, 'property');
      console.log('✅ 模拟登录完成');
    }

    // 跳转到GIS管理页面并激活绘制模式
    if (mode === 'polygon') {
      console.log('🧭 准备跳转到GIS管理页面进行绘制...');
      console.log('📍 当前路由:', router.currentRoute.value.path);
      console.log('🎯 目标路由: /property/gis-management');

      // 使用 await 确保路由跳转完成
      const result = await router.push({
        path: '/property/gis-management',
        query: {
          tab: 'grid',
          action: 'draw'
        }
      });

      console.log('✅ 绘制模式路由跳转结果:', result);
      console.log('📍 跳转后路由:', router.currentRoute.value.path);

      // 如果跳转成功，显示成功消息
      if (router.currentRoute.value.path === '/property/gis-management') {
        console.log('🎉 成功跳转到GIS管理页面！');
      } else {
        console.warn('⚠️ 路由跳转可能未成功');
        // 尝试备用跳转方式
        window.location.href = '/property/gis-management?tab=grid&action=draw';
      }
    } else {
      console.log('🔄 切换绘制模式:', mode);
      drawingMode.value = drawingMode.value === mode ? null : mode;
    }
  } catch (error) {
    console.error('❌ 绘制模式切换失败:', error);
    dialog.showError('操作失败: ' + error.message, '操作失败');
  }
};

const toggleEditingMode = () => {
  editingMode.value = !editingMode.value;
};

const clearSelection = () => {
  selectedGrid.value = null;
};

const importGrids = () => {
  console.log('导入网格数据');
};

const exportGrids = () => {
  console.log('导出网格数据');
};

const onMarkerClick = (marker) => {
  console.log('点击标记:', marker);
};

const onPolygonClick = (polygon) => {
  console.log('点击多边形:', polygon);
};

const onDrawCreated = (feature) => {
  console.log('绘制完成:', feature);
};

const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN');
};

// GIS功能导航方法
const navigateToGISManagement = () => {
  router.push('/property/gis-management');
};

const navigateToEventMap = () => {
  router.push('/property/event-map');
};

const navigateToPatrolManagement = () => {
  router.push('/property/patrol-management');
};

const navigateToDataOverview = () => {
  router.push('/property/data-overview');
};

/**
 * 刷新网格数据
 */
const refreshGridData = async () => {
  isLoadingGrids.value = true;
  try {
    const gridData = await loadGridsFromBackend();
    grids.value = gridData;

    console.log('✅ GridManagement: 网格数据刷新完成:', gridData.length, '个网格');
  } catch (error) {
    console.error('❌ GridManagement: 刷新网格数据失败:', error);
  } finally {
    isLoadingGrids.value = false;
  }
};

// 生命周期
onMounted(async () => {
  console.log('🚀 网格管理页面初始化');

  // 初始化用户状态
  userStore.initializeUser();
  console.log('👤 初始化后用户状态:', {
    isAuthenticated: userStore.isAuthenticated,
    userType: userStore.userType,
    user: userStore.user
  });

  // 如果用户未认证，模拟登录
  if (!userStore.isAuthenticated) {
    console.log('⚠️ 用户未认证，执行模拟登录...');
    userStore.login({
      id: 1,
      name: '测试物业用户',
      email: '<EMAIL>',
      role: 'property_manager'
    }, 'property');
    console.log('✅ 模拟登录完成');
  }

  // 加载网格数据
  await refreshGridData();

  console.log('📍 Router对象:', router);
  console.log('🔧 createGrid函数:', createGrid);
  console.log('🔧 toggleDrawingMode函数:', toggleDrawingMode);

  // 测试按钮是否存在
  setTimeout(() => {
    const createBtn = document.querySelector('.btn.primary');
    const drawBtn = document.querySelector('.tool-btn');
    console.log('🔍 新建网格按钮:', createBtn);
    console.log('🔍 绘制网格按钮:', drawBtn);

    if (createBtn) {
      console.log('✅ 新建网格按钮已找到');
      // 测试按钮点击事件
      createBtn.addEventListener('click', () => {
        console.log('🎯 按钮点击事件被触发！');
      });
    } else {
      console.log('❌ 新建网格按钮未找到');
    }

    if (drawBtn) {
      console.log('✅ 绘制网格按钮已找到');
    } else {
      console.log('❌ 绘制网格按钮未找到');
    }
  }, 1000);
});
</script>

<style scoped>
.grid-management {
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 2.5em;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 1.2em;
}

.management-content {
  padding: 30px;
}

.toolbar {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.gis-nav {
  display: flex;
  gap: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.nav-btn {
  background: white;
  border: 1px solid #e1e8ed;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  color: #666;
}

.nav-btn:hover {
  background: #4a90e2;
  color: white;
  border-color: #4a90e2;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.nav-icon {
  font-size: 14px;
}

.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.btn.primary {
  background: #4a90e2;
  color: white;
}

.btn.primary:hover {
  background: #357abd;
}

.btn.secondary {
  background: #6c757d;
  color: white;
}

.btn.secondary:hover {
  background: #5a6268;
}

.btn-icon {
  font-size: 14px;
}

.search-box {
  display: flex;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.search-input {
  padding: 8px 12px;
  border: none;
  outline: none;
  width: 200px;
  font-size: 14px;
}

.search-btn {
  background: #f8f9fa;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
}

.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  overflow: hidden;
}

.content-layout {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  min-height: 600px;
}

.grid-list-section {
  border-right: 1px solid #e1e8ed;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.section-header h2 {
  margin: 0;
  font-size: 1.4em;
  color: #333;
}

.grid-count {
  color: #666;
  font-size: 14px;
}

.grid-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.grid-item {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.grid-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.grid-item.selected {
  border-color: #4a90e2;
  background: #f0f7ff;
}

.grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.grid-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2em;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

.grid-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.info-item .label {
  color: #666;
  font-weight: 500;
}

.info-item .value {
  color: #333;
  font-weight: 600;
}

.grid-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #e9ecef;
}

.action-btn.edit:hover {
  border-color: #4a90e2;
  color: #4a90e2;
}

.action-btn.view:hover {
  border-color: #28a745;
  color: #28a745;
}

.action-btn.danger:hover {
  border-color: #dc3545;
  color: #dc3545;
}

.map-section {
  display: flex;
  flex-direction: column;
}

.map-tools {
  display: flex;
  gap: 8px;
}

.tool-btn {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.tool-btn:hover {
  background: #e9ecef;
  border-color: #4a90e2;
}

.tool-btn.active {
  background: #4a90e2;
  color: white;
  border-color: #4a90e2;
}

.map-container {
  flex: 1;
  padding: 20px;
}

.details-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 12px rgba(0,0,0,0.15);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.panel-header h3 {
  margin: 0;
  font-size: 1.2em;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.close-btn:hover {
  background: #e9ecef;
  color: #333;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
}

.detail-tabs {
  display: flex;
  border-bottom: 1px solid #e1e8ed;
}

.tab-btn {
  background: none;
  border: none;
  padding: 15px 20px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.tab-btn:hover {
  color: #4a90e2;
  background: #f8f9fa;
}

.tab-btn.active {
  color: #4a90e2;
  border-bottom-color: #4a90e2;
  background: #f8f9fa;
}

.tab-content {
  padding: 20px;
}

.basic-info .info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.basic-info .info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.basic-info .info-item label {
  font-weight: 600;
  color: #666;
  font-size: 14px;
}

.basic-info .info-item span {
  color: #333;
  font-size: 16px;
}

.status-text.active {
  color: #28a745;
  font-weight: 600;
}

.status-text.inactive {
  color: #dc3545;
  font-weight: 600;
}

.statistics-info .stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-number {
  font-size: 2em;
  font-weight: bold;
  color: #4a90e2;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.history-info .history-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.history-item {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #4a90e2;
}

.history-time {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.history-action {
  font-weight: 600;
  color: #333;
  margin-bottom: 3px;
}

.history-user {
  font-size: 14px;
  color: #666;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.dialog-header h3 {
  margin: 0;
  font-size: 1.3em;
  color: #333;
}

.dialog-content {
  padding: 20px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.form-group input,
.form-group select {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-layout {
    grid-template-columns: 1fr;
  }

  .grid-list-section {
    border-right: none;
    border-bottom: 1px solid #e1e8ed;
  }

  .details-panel {
    width: 100%;
    position: relative;
    height: auto;
    box-shadow: none;
    border-top: 1px solid #e1e8ed;
  }
}

@media (max-width: 768px) {
  .management-content {
    padding: 15px;
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    justify-content: center;
  }

  .gis-nav {
    flex-wrap: wrap;
    justify-content: center;
  }

  .nav-btn {
    font-size: 11px;
    padding: 6px 10px;
  }

  .search-input {
    width: 150px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

/* 加载状态样式 */
.loading-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.empty-message p {
  margin-bottom: 16px;
  font-size: 16px;
}
</style>

# 使用须知
npm install
npm install leaflet vue-leaflet chart.js vue-chartjs @turf/turf
npm install axios
npm run dev

# Vue 3 + Vite

This template should help get you started developing with Vue 3 in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

## Recommended IDE Setup

- [VS Code](https://code.visualstudio.com/) + [Vue - Official](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (previously Volar) and disable Vetur

### 项目架构分析
这是一个基于 Vue 3 的前端项目，使用了 Vite 作为构建工具。从 package.json 文件中可以看到，项目主要依赖以下几个核心库：

- vue : 核心的视图库。
- vue-router : 用于处理前端路由，实现单页面应用（SPA）的页面跳转。
- pinia : 用于状态管理，它是一个轻量级、模块化的 Vue 状态管理库，可以看作是 Vuex 的替代品。
项目的目录结构也比较清晰：

- src/components : 存放可复用的 Vue 组件。
- src/views : 存放各个页面的视图组件，如登录页 ( Login.vue )、主页等。
- src/layouts : 存放布局组件，如 AppLayout.vue 和 SidebarLayout.vue ，用于构建不同页面的通用布局结构。
- src/router.js : 定义了应用的路由规则，将 URL 路径映射到对应的视图组件。
- src/stores : 存放 Pinia 的状态管理模块。目前有一个 userStore.js ，用于管理用户的登录状态、信息和角色（物业或居民）。
- src/main.js : 应用的入口文件，在这里初始化 Vue 应用、Pinia 和 Vue Router。
从 src/router.js 的内容来看，应用设置了路由守卫（ router.beforeEach ），用于在每次路由跳转前检查用户的登录状态和权限。如果用户未登录，会尝试从 localStorage 中恢复用户信息。这是一种常见的前端持久化登录状态的实现方式。

### 登录密码
- 物业端和居民端的默认用户名和密码都是：admin / password

### 项目运行出错
- 可以尝试修改json文件
/**
 * 实名认证相关API服务
 */

// 根据环境自动选择API基础URL
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? '/api'
  : 'http://localhost:8080/api';

// 开发模式开关 - 当后端服务不可用时，可以启用模拟模式
const USE_MOCK_API = false; // 设置为 false 使用真实API

/**
 * 获取认证token
 */
const getAuthToken = () => {
  // 优先从auth_data中获取token
  try {
    const authData = localStorage.getItem('auth_data') || sessionStorage.getItem('auth_data');
    if (authData) {
      const parsed = JSON.parse(authData);
      if (parsed.token) {
        console.log('🔑 从auth_data获取到token:', parsed.token.substring(0, 20) + '...');
        return parsed.token;
      }
    }
  } catch (error) {
    console.warn('⚠️ 解析auth_data失败:', error);
  }

  // 备用方案：从其他可能的存储位置获取
  const fallbackToken = localStorage.getItem('auth_token') ||
                        localStorage.getItem('userToken') ||
                        'mock-token';
  console.log('🔑 使用备用token:', fallbackToken.substring(0, 20) + '...');
  return fallbackToken;
};

/**
 * 开发环境模拟API响应
 */
const mockApiResponse = (url, options) => {
  console.log('🎭 使用模拟API响应:', url);

  // 模拟网络延迟
  return new Promise((resolve) => {
    setTimeout(() => {
      if (url.includes('/identity-verification') && options.method === 'POST') {
        resolve({
          success: true,
          message: '认证申请提交成功！（模拟响应）',
          code: 200,
          data: null
        });
      } else if (url.includes('/users/me/identity-verification')) {
        // 模拟未认证状态 - 返回空数据表示用户尚未进行实名认证
        resolve({
          success: true,
          message: '查询成功',
          code: 200,
          data: null // null 表示用户尚未进行实名认证
        });
      } else if (url.includes('/auth/ocr') && options.method === 'POST') {
        // 模拟OCR识别响应
        resolve({
          success: true,
          message: 'OCR识别成功（模拟响应）',
          code: 200,
          data: {
            ocrIdNumber: '110101199001011234',
            ocrName: '张三',
            rawOcrData: {
              words_result: {
                姓名: { words: '张三' },
                公民身份号码: { words: '110101199001011234' },
                出生: { words: '1990年01月01日' },
                住址: { words: '北京市东城区某某街道某某号' },
                民族: { words: '汉' },
                性别: { words: '男' }
              },
              log_id: 1234567890,
              direction: 0,
              image_status: 'normal',
              risk_type: 'normal'
            }
          }
        });
      } else {
        resolve({
          success: false,
          message: '未知的API端点',
          code: 404
        });
      }
    }, 1000); // 模拟1秒延迟
  });
};

/**
 * 通用API请求方法
 */
const apiRequest = async (url, options = {}) => {
  // 如果启用了模拟API，直接返回模拟响应
  if (USE_MOCK_API) {
    return await mockApiResponse(url, options);
  }

  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getAuthToken()}`
    }
  };

  const finalOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers
    }
  };

  try {
    console.log(`🌐 API请求: ${options.method || 'GET'} ${url}`, {
      options: finalOptions,
      body: options.body
    });

    const response = await fetch(url, finalOptions);

    // 检查响应状态
    if (!response.ok) {
      console.error(`❌ HTTP错误: ${response.status} ${response.statusText}`);

      // 尝试解析错误响应
      let errorMessage = `请求失败 (${response.status})`;
      try {
        const errorResult = await response.json();
        errorMessage = errorResult.msg || errorResult.message || errorMessage;
      } catch (e) {
        // 如果无法解析JSON，使用默认错误消息
      }

      return {
        success: false,
        message: errorMessage,
        code: response.status
      };
    }

    const result = await response.json();

    console.log(`📦 API响应: ${url}`, {
      status: response.status,
      result
    });

    return {
      success: response.ok && result.code === 200,
      data: result.data,
      message: result.msg,
      code: result.code,
      raw: result
    };
  } catch (error) {
    console.error(`❌ API请求失败: ${url}`, error);

    // 更详细的错误处理
    let errorMessage = '网络请求失败';
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      errorMessage = '无法连接到服务器，请检查网络连接或确认后端服务是否启动';
    } else if (error.message.includes('CORS')) {
      errorMessage = '跨域请求被阻止，请检查服务器CORS配置';
    }

    return {
      success: false,
      message: errorMessage,
      error
    };
  }
};

/**
 * 实名认证API
 */
export const identityApi = {
  /**
   * 提交实名认证申请
   * @param {Object} data - 认证数据
   * @param {string} data.submittedIdCard - 提交的身份证号
   * @param {string} data.submittedName - 提交的姓名
   * @param {string} data.sessionId - 认证会话ID
   */
  submitVerification: async (data) => {
    return await apiRequest(`${API_BASE_URL}/auth/identity-verification`, {
      method: 'POST',
      body: JSON.stringify({
        submittedIdCard: data.submittedIdCard,
        submittedName: data.submittedName,
        sessionId: data.sessionId
      })
    });
  },

  /**
   * OCR身份证识别
   * @param {Object} data - OCR数据
   * @param {string} data.sessionId - 认证会话ID
   * @param {string} data.idCardFrontUrl - 身份证正面照URL
   */
  ocrIdentityCard: async (data) => {
    return await apiRequest(`${API_BASE_URL}/auth/identity-verification/ocr`, {
      method: 'POST',
      body: JSON.stringify({
        sessionId: data.sessionId,
        idCardFrontUrl: data.idCardFrontUrl
      })
    });
  },

  /**
   * 查询个人实名认证信息
   * 注意：由于后端暂未实现此接口，暂时返回模拟数据
   */
  getVerificationInfo: async () => {
    // 暂时返回模拟的未认证状态，避免调用不存在的接口
    console.log('⚠️ 查询认证信息接口暂未实现，返回模拟数据');
    console.log('💡 用户可以正常提交实名认证申请，后端会处理认证请求');
    return {
      success: true,
      message: '查询成功（模拟数据）',
      code: 200,
      data: null // null 表示用户尚未进行实名认证，可以提交认证申请
    };
  },

  /**
   * 查询认证状态
   * @param {string} sessionId - 会话ID
   */
  getVerificationStatus: async (sessionId) => {
    return await apiRequest(`${API_BASE_URL}/auth/identity-verification/status/${sessionId}`, {
      method: 'GET'
    });
  },

  /**
   * 上传身份证照片
   * @param {File} file - 图片文件
   * @param {string} sessionId - 会话ID
   */
  uploadIdCardImage: async (file, sessionId) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('sessionId', sessionId);

    return await apiRequest(`${API_BASE_URL}/auth/identity-verification/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`
        // 不设置Content-Type，让浏览器自动设置multipart/form-data
      },
      body: formData
    });
  },

  /**
   * OCR身份证识别（直接上传文件）
   * @param {File} file - 图片文件
   * @param {string} sessionId - 会话ID
   */
  ocrIdentityCardFile: async (file, sessionId) => {
    console.log('🔍 开始OCR识别请求:', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      sessionId: sessionId
    });

    const formData = new FormData();
    formData.append('image', file);
    formData.append('session_id', sessionId);

    // 获取token
    const token = getAuthToken();
    console.log('🔑 使用token:', token ? token.substring(0, 20) + '...' : 'null');

    try {
      const response = await fetch(`${API_BASE_URL}/auth/ocr`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
          // 不设置Content-Type，让浏览器自动设置multipart/form-data
        },
        body: formData
      });

      console.log('📡 OCR响应状态:', response.status, response.statusText);

      if (!response.ok) {
        console.error(`❌ OCR请求失败: ${response.status} ${response.statusText}`);

        // 尝试解析错误响应
        let errorMessage = `OCR请求失败 (${response.status})`;
        try {
          const errorResult = await response.json();
          errorMessage = errorResult.msg || errorResult.message || errorMessage;
          console.error('❌ 错误详情:', errorResult);
        } catch (e) {
          console.error('❌ 无法解析错误响应');
        }

        return {
          success: false,
          message: errorMessage,
          code: response.status
        };
      }

      const result = await response.json();
      console.log('📦 OCR识别结果:', result);

      // 统一返回格式
      return {
        success: true,
        data: result.data || result,
        message: result.msg || result.message || 'OCR识别成功',
        code: result.code || 200,
        raw: result // 保留原始响应
      };

    } catch (error) {
      console.error('❌ OCR请求异常:', error);
      return {
        success: false,
        message: error.message || 'OCR请求失败',
        code: 0
      };
    }
  }
};

/**
 * 工具函数
 */
export const identityUtils = {
  /**
   * 生成会话ID
   */
  generateSessionId: () => {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  },

  /**
   * 验证身份证号码格式
   * @param {string} idCard - 身份证号码
   */
  validateIdCard: (idCard) => {
    if (!idCard) return { valid: false, message: '身份证号码不能为空' };
    
    // 基本格式验证
    const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!idCardRegex.test(idCard)) {
      return { valid: false, message: '身份证号码格式不正确' };
    }

    // 校验码验证
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    
    let sum = 0;
    for (let i = 0; i < 17; i++) {
      sum += parseInt(idCard[i]) * weights[i];
    }
    
    const checkCode = checkCodes[sum % 11];
    const lastChar = idCard[17].toUpperCase();
    
    if (checkCode !== lastChar) {
      return { valid: false, message: '身份证号码校验位不正确' };
    }

    return { valid: true, message: '身份证号码格式正确' };
  },

  /**
   * 验证姓名格式
   * @param {string} name - 姓名
   */
  validateName: (name) => {
    if (!name) return { valid: false, message: '姓名不能为空' };
    
    const nameRegex = /^[\u4e00-\u9fa5]{2,10}$/;
    if (!nameRegex.test(name)) {
      return { valid: false, message: '姓名应为2-10个中文字符' };
    }

    return { valid: true, message: '姓名格式正确' };
  },

  /**
   * 验证图片文件
   * @param {File} file - 图片文件
   */
  validateImageFile: (file) => {
    if (!file) return { valid: false, message: '请选择图片文件' };
    
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      return { valid: false, message: '请选择图片文件' };
    }

    // 检查文件大小 (5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      return { valid: false, message: '图片文件大小不能超过5MB' };
    }

    // 检查图片格式
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
      return { valid: false, message: '仅支持JPG、PNG格式的图片' };
    }

    return { valid: true, message: '图片文件格式正确' };
  }
};

export default identityApi;
